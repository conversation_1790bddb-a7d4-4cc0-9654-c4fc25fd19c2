# SQLite 打包配置说明

## 📋 已完成的配置

### 1. 模块配置
在 `manifest.config.ts` 中已添加 SQLite 模块：
```typescript
modules: {
  Maps: {},
  Messaging: {},
  Contacts: {},
  Camera: {},
  SQLite: {}, // ✅ 已添加
},
```

### 2. Android 权限配置
已添加必要的存储权限：
```typescript
permissions: [
  // ... 其他权限
  '<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>',
  '<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>',
],
```

## 🚀 打包步骤

### 方法一：HBuilderX 云端打包（推荐）

1. **打开 HBuilderX**
   - 确保项目已正确配置

2. **检查模块配置**
   - 打开 `manifest.json` 文件
   - 在 "App模块配置" 中确认已勾选 "SQLite" 模块

3. **云端打包**
   - 点击 "发行" → "云端打包" → "Android App"
   - 选择 "使用DCloud公用证书" 或上传自己的证书
   - 确保在打包配置中勾选了 "SQLite" 模块
   - 点击 "打包" 等待完成

### 方法二：HBuilderX 本地打包

1. **安装 Android SDK**
   - 确保已安装 Android SDK
   - 配置环境变量

2. **本地打包**
   - 点击 "发行" → "原生App-本地打包" → "Android App"
   - 选择 "使用DCloud公用证书"
   - 确保勾选 "SQLite" 模块
   - 点击 "打包"

### 方法三：离线打包

1. **下载离线打包SDK**
   - 从 DCloud 官网下载对应版本的离线打包SDK

2. **配置离线工程**
   - 将项目代码复制到离线工程中
   - 确保 `build.gradle` 中包含 SQLite 相关依赖

## ⚠️ 注意事项

### 1. 数据库文件路径
确保使用正确的数据库文件路径：
```typescript
// 推荐使用应用私有文档目录
this.db = plus.sqlite.openDatabase({
  name: this.dbName,
  path: '_doc/assets.db' // ✅ 使用 _doc 目录
})
```

### 2. 权限申请
在 Android 6.0+ 系统中，需要在运行时申请存储权限：
```typescript
// 在需要时申请权限
uni.request({
  url: 'plus://requestPermissions',
  data: {
    permissions: ['android.permission.WRITE_EXTERNAL_STORAGE']
  }
})
```

### 3. 测试建议
- 在真机上测试 SQLite 功能
- 确保数据库文件能正常创建和读写
- 测试数据插入和查询功能

## 🔧 故障排除

### 1. 数据库打开失败
- 检查文件路径是否正确
- 确保应用有存储权限
- 检查数据库文件是否已创建

### 2. 打包后功能异常
- 确认 SQLite 模块已正确打包
- 检查权限配置是否完整
- 查看控制台错误信息

### 3. 性能优化
- 大量数据操作时使用事务
- 适当使用索引提高查询性能
- 定期清理不需要的数据

## 📱 平台支持

- ✅ **Android**: 完全支持
- ✅ **iOS**: 完全支持  
- ⚠️ **H5**: 使用 localStorage 模拟
- ❌ **小程序**: 不支持 SQLite，需要使用云数据库

## 🎯 验证方法

打包完成后，可以通过以下方式验证 SQLite 功能：

1. 打开应用
2. 进入盘点列表页面
3. 点击 "同步数据到本地" 按钮
4. 查看是否显示成功提示
5. 点击 "查看统计信息" 验证数据存储
6. 使用查询功能验证数据检索

如果以上步骤都能正常执行，说明 SQLite 插件已成功集成。
