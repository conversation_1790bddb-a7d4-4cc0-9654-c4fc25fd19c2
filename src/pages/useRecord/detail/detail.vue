<route lang="json5" type="page">
{
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "领用详情"
  }
}
</route>

<template>
	<view class="detail-container">
		<nav-bar>领用详情</nav-bar>
		<view class="info-section">
			<view class="info-card">
				<view class="info-item">
					<view class="info-label">申请单号</view>
					<view class="info-value">{{ detailData.number }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">申请人</view>
					<view class="info-value">{{ detailData.userId_dictText }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">创建时间</view>
					<view class="info-value">{{ formatDateTime(detailData.createTime) }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">资产编号</view>
					<view class="info-value">{{ detailData.assetNumber }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">资产名称</view>
					<view class="info-value">{{ detailData.assetNumber_dictText }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">存储位置编号</view>
					<view class="info-value">{{ detailData.storageLocation }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">存储位置</view>
					<view class="info-value">{{ detailData.storageLocation_dictText }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 响应式数据
const detailData = ref({})

// 页面加载时获取数据
onLoad((options) => {
	if (options.data) {
		try {
			detailData.value = JSON.parse(decodeURIComponent(options.data))
		} catch (e) {
			console.error('解析数据失败:', e)
		}
	}
})

// 格式化日期时间
const formatDateTime = (timeStr) => {
	if (!timeStr) return ''
	const date = new Date(timeStr)
	const year = date.getFullYear()
	const month = (date.getMonth() + 1).toString().padStart(2, '0')
	const day = date.getDate().toString().padStart(2, '0')
	const hours = date.getHours().toString().padStart(2, '0')
	const minutes = date.getMinutes().toString().padStart(2, '0')
	const seconds = date.getSeconds().toString().padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 处理操作
const handleAction = () => {
	uni.showToast({
		title: '操作功能待实现',
		icon: 'none'
	})
}
</script>

<style scoped>
.detail-container {
	padding-top: 20rpx;
	padding-bottom: 40rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部卡片 */
.header-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 40rpx;
	margin: 20rpx;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
}

.header-icon {
	font-size: 60rpx;
	margin-right: 30rpx;
}

.header-content {
	flex: 1;
}

.title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 10rpx;
}

.subtitle {
	font-size: 28rpx;
	color: #666;
}

.status-badge {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
}

/* 信息区块 */
.info-section {
	margin-bottom: 30rpx;
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	padding-left: 20rpx;
}

.title-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.section-title text {
	font-size: 32rpx;
	font-weight: 600;
	color: white;
}

.info-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin: 0 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
	min-width: 200rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	text-align: right;
	word-break: break-all;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	gap: 20rpx;
	margin: 40rpx 20rpx 0;
	padding-bottom: 40rpx;
}

.btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.btn-secondary {
	background: rgba(255, 255, 255, 0.2);
	color: white;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:active {
	background: rgba(255, 255, 255, 0.3);
	transform: scale(0.98);
}

.btn-primary {
	background: white;
	color: #667eea;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.btn-primary:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.15);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.detail-container {
		padding: 15rpx;
	}
	
	.header-card {
		padding: 30rpx;
	}
	
	.info-card {
		padding: 25rpx;
	}
	
	.info-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 10rpx;
	}
	
	.info-value {
		text-align: left;
	}
}
</style>
