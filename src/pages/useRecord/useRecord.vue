<route lang="json5" type="page">
{
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "领用记录"
  }
}
</route>

<template>
	<view>
		<nav-bar backState="1000" title="领用">领用</nav-bar>
		<!--滚动加载列表-->
		<mescroll-body ref="mescrollRef" bottom="88"  @init="mescrollInit" :up="upOption" :down="downOption" @down="downCallback" @up="upCallback">
		    <view class="list-container">
				<!-- 数据列表 -->
				<view class="list-item" v-for="(item,index) in list" :key="index" @click="goToDetail(item)">
					<view class="item-header">
						<view class="item-number">{{ item.number }}</view>
						<view class="item-time">{{ formatTime(item.createTime) }}</view>
					</view>
					<view class="item-content">
						<view class="item-row">
							<text class="label">申请人：</text>
							<text class="value">{{ item.userId_dictText }}</text>
						</view>
						<view class="item-row">
							<text class="label">资产：</text>
							<text class="value">{{ item.assetNumber_dictText }}</text>
						</view>
						<view class="item-row">
							<text class="label">位置：</text>
							<text class="value">{{ item.storageLocation_dictText }}</text>
						</view>
					</view>
					<view class="item-footer">
						<view class="status-tag">详情</view>
					</view>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-state" v-if="list.length === 0 && !loading">
					<view class="icon">📋</view>
					<view class="text">暂无使用记录</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import mescrollBody from "@/components/mescroll-uni/mescroll-body.vue";
	import { http } from "@/utils/http";

	export default {
		name: 'asset_use_records',
		mixins: [MescrollMixin],
		components: {
			mescrollBody
		},
		data() {
			return {
				url: "/jeecg/assetUseRecords/list",
				list: [], // 数据列表
				loading: false, // 加载状态
				upOption: {
					use: true,
					auto: true,
					page: {
						num: 0,
						size: 10
					}
				},
				downOption: {
					use: true
				}
			};
		},
		methods: {
			goHome(){
                // 跳转到 tabbar 页面需要使用 switchTab
                uni.switchTab({
                    url: '/pages/index/index'
                })
			},
			// 跳转到详情页面
			goToDetail(item) {
				const data = encodeURIComponent(JSON.stringify(item))
				uni.navigateTo({
					url: `/pages/useRecord/detail/detail?data=${data}`
				})
			},
			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				return `${month}-${day} ${hours}:${minutes}`;
			},
			// 上拉加载的回调
			upCallback(page) {
				this.loading = true;
				
				// 构建请求参数
				const params = {
					pageNo: page.num,
					pageSize: page.size
				};
				
				// 调用接口获取数据
				http.get(this.url, params).then(res => {
					this.loading = false;
					
					if (res.code === 200) {
						const data = res.result || {};
						const list = data.records || [];
						
						if (page.num === 1) {
							// 第一页，直接赋值
							this.list = list;
						} else {
							// 后续页，追加数据
							this.list = this.list.concat(list);
						}
						
						// 结束加载
						this.mescroll.endBySize(list.length, page.size);
					} else {
						// 请求失败
						this.mescroll.endErr();
					}
				}).catch(err => {
					this.loading = false;
					console.error('请求失败:', err);
					this.mescroll.endErr();
				});
			}
		}
	}
</script>

<style scoped>
.list-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.list-item {
	background-color: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;
}

.list-item:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.item-number {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.item-time {
	font-size: 24rpx;
	color: #999;
	background-color: #f8f8f8;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.item-content {
	margin-bottom: 20rpx;
}

.item-row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.item-row:last-child {
	margin-bottom: 0;
}

.label {
	font-size: 28rpx;
	color: #666;
	width: 120rpx;
	flex-shrink: 0;
}

.value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	word-break: break-all;
}

.item-footer {
	display: flex;
	justify-content: flex-end;
}

.status-tag {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	font-size: 24rpx;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-weight: 500;
}

/* 空状态样式 */
.empty-state {
	text-align: center;
	padding: 100rpx 40rpx;
	color: #999;
}

.empty-state .icon {
	font-size: 120rpx;
	margin-bottom: 20rpx;
	opacity: 0.5;
}

.empty-state .text {
	font-size: 28rpx;
}
</style>

