<template>
  <view class="sqlite-diagnostic">
    <view class="header">
      <text class="title">SQLite 诊断工具</text>
    </view>
    
    <view class="section">
      <view class="section-title">环境检测</view>
      <button @click="checkEnvironment" :loading="loading" class="btn">检测环境</button>
      <view v-if="envResults.length > 0" class="results">
        <view v-for="(result, index) in envResults" :key="index" class="result-item">
          <text class="result-label">{{ result.label }}:</text>
          <text class="result-value" :class="result.status">{{ result.value }}</text>
        </view>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">SQLite模块测试</view>
      <button @click="testSQLiteModule" :loading="loading" class="btn">测试SQLite模块</button>
      <view v-if="moduleTestResult" class="test-result">
        <text class="test-label">模块测试结果:</text>
        <text class="test-value" :class="moduleTestResult.success ? 'success' : 'error'">
          {{ moduleTestResult.message }}
        </text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">数据库操作测试</view>
      <button @click="testDatabaseOperations" :loading="loading" class="btn">测试数据库操作</button>
      <view v-if="dbTestResult" class="test-result">
        <text class="test-label">数据库测试结果:</text>
        <text class="test-value" :class="dbTestResult.success ? 'success' : 'error'">
          {{ dbTestResult.message }}
        </text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">性能测试</view>
      <button @click="performanceTest" :loading="loading" class="btn">性能测试</button>
      <view v-if="performanceResults.length > 0" class="results">
        <view v-for="(result, index) in performanceResults" :key="index" class="result-item">
          <text class="result-label">{{ result.label }}:</text>
          <text class="result-value">{{ result.value }}</text>
        </view>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">错误日志</view>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">[{{ log.time }}]</text>
          <text class="log-content">{{ log.content }}</text>
        </view>
      </scroll-view>
      <button @click="clearLogs" class="btn btn-secondary">清空日志</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { testSQLiteModule as testSQLiteModuleUtil, testDatabaseOperations as testDatabaseOperationsUtil } from '@/utils/sqliteTest'
import { sqliteManager } from '@/utils/sqlite'

// 响应式数据
const loading = ref(false)
const envResults = ref<Array<{label: string, value: string, status: string}>>([])
const moduleTestResult = ref<{success: boolean, message: string} | null>(null)
const dbTestResult = ref<{success: boolean, message: string} | null>(null)
const performanceResults = ref<Array<{label: string, value: string}>>([])
const logs = ref<Array<{time: string, content: string}>>([])

// 添加日志
const addLog = (content: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift({ time: timestamp, content })
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

// 检测环境
const checkEnvironment = async () => {
  loading.value = true
  envResults.value = []
  
  try {
    addLog('开始环境检测...')
    
    // 检测plus环境
    const plusAvailable = typeof plus !== 'undefined'
    envResults.value.push({
      label: 'Plus环境',
      value: plusAvailable ? '可用' : '不可用',
      status: plusAvailable ? 'success' : 'error'
    })
    
    if (plusAvailable) {
      // 检测SQLite模块
      const sqliteAvailable = !!(plus as any).sqlite
      envResults.value.push({
        label: 'SQLite模块',
        value: sqliteAvailable ? '可用' : '不可用',
        status: sqliteAvailable ? 'success' : 'error'
      })
      
      if (sqliteAvailable) {
        // 检测SQLite方法
        const sqliteMethods = Object.keys((plus as any).sqlite)
        envResults.value.push({
          label: 'SQLite方法',
          value: sqliteMethods.join(', '),
          status: 'success'
        })
        
        // 检测设备信息
        const deviceInfo = (plus as any).device
        if (deviceInfo) {
          envResults.value.push({
            label: '设备型号',
            value: deviceInfo.model || '未知',
            status: 'success'
          })
          envResults.value.push({
            label: '系统版本',
            value: deviceInfo.version || '未知',
            status: 'success'
          })
        }
      }
    }
    
    addLog('环境检测完成')
    
  } catch (error) {
    addLog(`环境检测失败: ${error}`)
    envResults.value.push({
      label: '检测错误',
      value: String(error),
      status: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 测试SQLite模块
const testSQLiteModule = async () => {
  loading.value = true
  moduleTestResult.value = null
  
  try {
    addLog('开始SQLite模块测试...')
    const result = testSQLiteModuleUtil()
    
    moduleTestResult.value = {
      success: result,
      message: result ? 'SQLite模块正常工作' : 'SQLite模块不可用'
    }
    
    addLog(`SQLite模块测试结果: ${result ? '成功' : '失败'}`)
    
  } catch (error) {
    addLog(`SQLite模块测试异常: ${error}`)
    moduleTestResult.value = {
      success: false,
      message: `测试异常: ${error}`
    }
  } finally {
    loading.value = false
  }
}

// 测试数据库操作
const testDatabaseOperations = async () => {
  loading.value = true
  dbTestResult.value = null
  
  try {
    addLog('开始数据库操作测试...')
    const result = await testDatabaseOperationsUtil()
    
    dbTestResult.value = {
      success: result,
      message: result ? '数据库操作正常' : '数据库操作失败'
    }
    
    addLog(`数据库操作测试结果: ${result ? '成功' : '失败'}`)
    
  } catch (error) {
    addLog(`数据库操作测试异常: ${error}`)
    dbTestResult.value = {
      success: false,
      message: `测试异常: ${error}`
    }
  } finally {
    loading.value = false
  }
}

// 性能测试
const performanceTest = async () => {
  loading.value = true
  performanceResults.value = []
  
  try {
    addLog('开始性能测试...')
    
    // 测试数据库初始化时间
    const initStart = Date.now()
    await sqliteManager.initDatabase()
    const initTime = Date.now() - initStart
    performanceResults.value.push({
      label: '数据库初始化时间',
      value: `${initTime}ms`
    })
    
    // 测试插入性能
    const insertStart = Date.now()
    const testAssets = []
    for (let i = 0; i < 100; i++) {
      testAssets.push({
        id: 9000 + i,
        number: `TEST-${i}`,
        name: `测试资产${i}`,
        model: '测试型号',
        categoryCode: '0',
        imageUrl: '',
        status: '0',
        storageLocation: '1',
        entryDate: null,
        additionMethod: '测试',
        originalValue: null,
        serviceLife: null,
        expiryYear: '2025',
        department: '测试部门',
        factoryArea: '',
        userId: 'test',
        createTime: new Date().toISOString(),
        version: '1.0.0'
      })
    }
    
    await sqliteManager.insertAssets(testAssets)
    const insertTime = Date.now() - insertStart
    performanceResults.value.push({
      label: '插入100条数据时间',
      value: `${insertTime}ms`
    })
    
    // 测试查询性能
    const queryStart = Date.now()
    const queryResults = await sqliteManager.queryAssetsByNumberRange({
      startNumber: 'TEST-0',
      endNumber: 'TEST-99',
      limit: 50
    })
    const queryTime = Date.now() - queryStart
    performanceResults.value.push({
      label: '查询50条数据时间',
      value: `${queryTime}ms`
    })
    
    // 测试统计性能
    const countStart = Date.now()
    const count = await sqliteManager.getAssetCount()
    const countTime = Date.now() - countStart
    performanceResults.value.push({
      label: '统计查询时间',
      value: `${countTime}ms`
    })
    
    performanceResults.value.push({
      label: '当前数据总数',
      value: `${count}条`
    })
    
    addLog('性能测试完成')
    
  } catch (error) {
    addLog(`性能测试失败: ${error}`)
    performanceResults.value.push({
      label: '测试错误',
      value: String(error)
    })
  } finally {
    loading.value = false
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  addLog('日志已清空')
}

// 页面加载时自动检测环境
onMounted(() => {
  addLog('SQLite诊断工具已启动')
  checkEnvironment()
})
</script>

<style scoped>
.sqlite-diagnostic {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 20rpx;
}

.btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  margin: 10rpx 0;
  font-size: 28rpx;
  width: 100%;
}

.btn-secondary {
  background-color: #6c757d;
}

.results {
  margin-top: 20rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.result-item:last-child {
  border-bottom: none;
}

.result-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.result-value {
  font-size: 28rpx;
  color: #333;
}

.result-value.success {
  color: #28a745;
  font-weight: bold;
}

.result-value.error {
  color: #dc3545;
  font-weight: bold;
}

.test-result {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.test-label {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.test-value {
  font-size: 28rpx;
  font-weight: bold;
}

.test-value.success {
  color: #28a745;
}

.test-value.error {
  color: #dc3545;
}

.log-container {
  height: 400rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  margin-bottom: 20rpx;
}

.log-item {
  margin-bottom: 10rpx;
  font-size: 24rpx;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 10rpx;
}

.log-content {
  color: #333;
}
</style>