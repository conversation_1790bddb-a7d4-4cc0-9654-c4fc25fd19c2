<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: 'demo演示',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="container">
    <view class="section">
      <text class="section-title">扫码功能</text>
      <button type="primary" @click="initScan">初始化</button>
      <button type="primary" @click="startScan">开始扫码</button>
      <button type="warn"    @click="stopScan">停止</button>
    </view>
    
    <view class="section">
      <text class="section-title">UHF功能</text>
      <button @click="initializeUHF">初始化UHF</button>
      <button @click="startInventory">开始盘点</button>
      <button @click="stopInventory">停止盘点</button>
    </view>
    
    <view class="section">
      <text class="section-title">SQLite数据库</text>
      <button @click="goToSQLiteDemo" class="btn-sqlite">SQLite演示</button>
      <button @click="goToSQLiteDiagnostic" class="btn-sqlite">SQLite诊断</button>
    </view>
    
    <view class="result">
      <text>结果：{{ result }}</text>
    </view>
  </view>
</template>

<script>
	import uhfManager from '@/utils/uhf-library.js'
export default {
  data() {
    return {
      result: ''
    };
  },
  onLoad() {
    console.log('Page Load');
  	
     var globalEvent = uni.requireNativePlugin('globalEvent');
  		globalEvent.addEventListener('uhf-tag-data', function(e) {
  		console.log('uhf-tag-data'+JSON.stringify(e));
  	});
    // 设置盘点回调
    // uhfManager.onInventoryTag(this.handleTags);
  },
  onUnload() {
    console.log('Page Unload');
    
    // 页面卸载时停止盘点
    if (this.isInventoryRunning) {
      this.stopInventory();
    }
    
    // 移除事件监听
    uhfManager.removeInventoryListener();
  },
  methods: {
    initScan() {
      const scan = uni.requireNativePlugin('ScanModule');
      scan.init({ mode: 3 }, (res) => {
        uni.showToast({ title: '初始化成功', icon: 'success' });
      });
    },
    startScan() {
      const scan = uni.requireNativePlugin('ScanModule');
      scan.startScan({ timeout: 5, type: 1 }, (res) => {
        this.result = JSON.stringify(res);
      }, (err) => {
        uni.showModal({ content: '扫码失败：' + JSON.stringify(err) });
      });
    },
    stopScan() {
      const scan = uni.requireNativePlugin('ScanModule');
      scan.stopScan();
      this.result = '已停止';
    },
	async initializeUHF() {
	  try {
	    this.resultMessage = '初始化中...';
	    const result = await uhfManager.initialize({
	      readPower: 30,
	      writePower: 30,
	      regional: 1
	    });
	    
	    this.resultMessage = result.message;
	  } catch (error) {
	    this.resultMessage = `初始化失败: ${error.message}`;
	    console.error('UHF initialization error:', error);
	  }
	},
	
	async startInventory() {
	  try {
	    this.resultMessage = '开始盘点...';
	    const result = await uhfManager.startInventory();

	    this.resultMessage = result.message;
	    if (result.success) {
	      this.isInventoryRunning = true;
	    }
	  } catch (error) {
	    this.resultMessage = `开始盘点失败: ${error.message}`;
	    console.error('Start inventory error:', error);
	  }
	},
	
	async stopInventory() {
	  try {
	    this.resultMessage = '停止盘点...';
	    const result = await uhfManager.stopInventory();
	    
	    this.resultMessage = result.message;
	    if (result.success) {
	      this.isInventoryRunning = false;
	    }
	  } catch (error) {
	    this.resultMessage = `停止盘点失败: ${error.message}`;
	    console.error('Stop inventory error:', error);
	  }
	},
	
	// SQLite相关跳转
	goToSQLiteDemo() {
	  uni.navigateTo({
	    url: '/pages/demo/sqliteDemo'
	  });
	},
	
	goToSQLiteDiagnostic() {
	  uni.navigateTo({
	    url: '/pages/demo/sqliteDiagnostic'
	  });
	},
  }
};
</script>

<style>
.container {
  padding: 40rpx;
}

.section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  border-left: 6rpx solid #007aff;
  padding-left: 20rpx;
}

button {
  margin: 20rpx 0;
  width: 100%;
}

.btn-sqlite {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  margin: 10rpx 0;
}

.btn-sqlite:active {
  background-color: #218838;
}

.result {
  margin-top: 40rpx;
  font-size: 32rpx;
  color: #333;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
}
</style>