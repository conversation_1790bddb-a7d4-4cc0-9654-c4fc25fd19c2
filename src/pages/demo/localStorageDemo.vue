<template>
  <view class="storage-demo">
    <view class="header">
      <text class="title">LocalStorage 资产数据演示</text>
      <text class="subtitle">真机可用的数据持久化方案</text>
    </view>

    <!-- 存储状态 -->
    <view class="status-card">
      <view class="status-title">存储状态</view>
      <view class="status-item">
        <text class="label">存储类型:</text>
        <text class="value" :class="storageTypeClass">{{ storageInfo.storageType || '未初始化' }}</text>
      </view>
      <view class="status-item">
        <text class="label">数据总数:</text>
        <text class="value">{{ storageInfo.totalCount || 0 }} 条</text>
      </view>
      <view class="status-item">
        <text class="label">存储大小:</text>
        <text class="value">{{ storageInfo.estimatedSize || '0 KB' }}</text>
      </view>
      <view class="status-item">
        <text class="label">最后更新:</text>
        <text class="value">{{ formatTime(storageInfo.lastUpdate) }}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions">
      <button @click="initStorage" :loading="loading" class="btn-primary">
        初始化存储
      </button>
      <button @click="generateTestData" :loading="loading" class="btn-secondary">
        生成测试数据
      </button>
      <button @click="clearAllData" :loading="loading" class="btn-danger">
        清空所有数据
      </button>
    </view>

    <!-- 查询功能 -->
    <view class="query-section">
      <view class="section-title">查询功能</view>

      <!-- 范围查询 -->
      <view class="query-form">
        <view class="form-title">范围查询</view>
        <view class="form-row">
          <input
            v-model="queryParams.startNumber"
            placeholder="起始编号"
            class="input"
          />
          <input
            v-model="queryParams.endNumber"
            placeholder="结束编号"
            class="input"
          />
        </view>
        <view class="form-row">
          <input
            v-model.number="queryParams.limit"
            placeholder="限制条数"
            type="number"
            class="input"
          />
          <button @click="queryByRange" :loading="loading" class="btn-query">
            范围查询
          </button>
        </view>
      </view>

      <!-- 精确查询 -->
      <view class="query-form">
        <view class="form-title">精确查询</view>
        <view class="form-row">
          <input
            v-model="exactQueryNumber"
            placeholder="资产编号"
            class="input"
          />
          <button @click="queryByExactNumber" :loading="loading" class="btn-query">
            精确查询
          </button>
        </view>
      </view>
    </view>

    <!-- 查询结果 -->
    <view class="results" v-if="queryResults.length > 0">
      <view class="section-title">查询结果 ({{ queryResults.length }} 条)</view>
      <scroll-view class="result-list" scroll-y>
        <view
          v-for="(asset, index) in queryResults"
          :key="asset.id"
          class="result-item"
        >
          <view class="asset-header">
            <text class="asset-number">{{ asset.number }}</text>
            <text class="asset-name">{{ asset.name }}</text>
          </view>
          <view class="asset-details">
            <text class="detail-item">型号: {{ asset.model }}</text>
            <text class="detail-item">部门: {{ asset.department }}</text>
            <text class="detail-item">位置: {{ asset.storageLocation }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 操作日志 -->
    <view class="logs">
      <view class="section-title">操作日志</view>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-text">{{ log }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { dataManager } from '@/utils/dataManager'
import { AssetData } from '@/types/asset'

// 响应式数据
const loading = ref(false)
const queryResults = ref<AssetData[]>([])
const logs = ref<string[]>([])
const exactQueryNumber = ref('')
const storageInfo = ref<any>({})

// 查询参数
const queryParams = reactive({
  startNumber: '',
  endNumber: '',
  limit: 10
})

// 计算属性
const storageTypeClass = computed(() => {
  return {
    'type-localstorage': true
  }
})

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '未知'
  try {
    return new Date(timeStr).toLocaleString()
  } catch {
    return timeStr
  }
}

// 更新存储信息
const updateStorageInfo = () => {
  storageInfo.value = dataManager.getStorageInfo()
}

// 初始化存储
const initStorage = async () => {
  loading.value = true
  try {
    addLog('开始初始化存储...')
    await dataManager.initDatabase()
    updateStorageInfo()
    addLog(`存储初始化成功，使用: ${storageInfo.value.storageType}`)

    uni.showToast({
      title: '初始化成功',
      icon: 'success'
    })
  } catch (error) {
    addLog(`存储初始化失败: ${error}`)
    uni.showToast({
      title: '初始化失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 生成测试数据
const generateTestData = async () => {
  loading.value = true
  try {
    addLog('开始生成测试数据...')

    const testAssets: AssetData[] = []
    const now = new Date().toISOString()

    for (let i = 1; i <= 100; i++) {
      testAssets.push({
        id: i,
        number: `ASSET-${String(i).padStart(6, '0')}`,
        name: `测试资产${i}`,
        model: `Model-${i % 10}`,
        categoryCode: `CAT-${i % 5}`,
        imageUrl: '',
        status: i % 2 === 0 ? '正常' : '维修中',
        storageLocation: `仓库${i % 3 + 1}`,
        entryDate: now,
        additionMethod: '批量导入',
        originalValue: Math.floor(Math.random() * 10000),
        serviceLife: 5,
        expiryYear: '2029',
        department: `部门${i % 4 + 1}`,
        factoryArea: `厂区${i % 2 + 1}`,
        userId: 'test_user',
        createTime: now,
        version: '1.0.0'
      })
    }

    await dataManager.insertAssets(testAssets)
    updateStorageInfo()
    addLog(`测试数据生成完成，共 ${testAssets.length} 条`)

    uni.showToast({
      title: '数据生成成功',
      icon: 'success'
    })
  } catch (error) {
    addLog(`生成测试数据失败: ${error}`)
    uni.showToast({
      title: '生成失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 范围查询
const queryByRange = async () => {
  loading.value = true
  try {
    addLog(`开始范围查询: ${queryParams.startNumber} - ${queryParams.endNumber}`)

    const results = await dataManager.queryAssetsByNumberRange({
      startNumber: queryParams.startNumber || undefined,
      endNumber: queryParams.endNumber || undefined,
      limit: queryParams.limit || undefined
    })

    queryResults.value = results
    addLog(`范围查询完成，找到 ${results.length} 条数据`)

  } catch (error) {
    addLog(`范围查询失败: ${error}`)
    uni.showToast({
      title: '查询失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 精确查询
const queryByExactNumber = async () => {
  if (!exactQueryNumber.value.trim()) {
    uni.showToast({
      title: '请输入资产编号',
      icon: 'none'
    })
    return
  }

  loading.value = true
  try {
    addLog(`开始精确查询: ${exactQueryNumber.value}`)

    const result = await dataManager.queryAssetByNumber(exactQueryNumber.value)
    queryResults.value = result ? [result] : []

    addLog(`精确查询完成，${result ? '找到' : '未找到'}数据`)

  } catch (error) {
    addLog(`精确查询失败: ${error}`)
    uni.showToast({
      title: '查询失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 清空所有数据
const clearAllData = async () => {
  const confirmed = await new Promise((resolve) => {
    uni.showModal({
      title: '确认清空',
      content: '确定要清空所有数据吗？此操作不可恢复。',
      success: (res) => resolve(res.confirm)
    })
  })

  if (!confirmed) return

  loading.value = true
  try {
    addLog('开始清空所有数据...')
    await dataManager.clearAllAssets()
    queryResults.value = []
    updateStorageInfo()
    addLog('所有数据已清空')

    uni.showToast({
      title: '清空成功',
      icon: 'success'
    })
  } catch (error) {
    addLog(`清空数据失败: ${error}`)
    uni.showToast({
      title: '清空失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 页面加载时初始化
onMounted(async () => {
  addLog('页面加载，准备初始化存储...')
  await initStorage()
})
</script>

<style scoped>
.storage-demo {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 10rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

.status-card {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.type-sqlite {
  color: #007aff;
}

.type-localstorage {
  color: #ff9500;
}

.actions {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.btn-primary {
  flex: 1;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.btn-secondary {
  flex: 1;
  background: #ff9500;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.btn-danger {
  flex: 1;
  background: #ff3b30;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.query-section {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.query-form {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.query-form:last-child {
  margin-bottom: 0;
}

.form-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.form-row {
  display: flex;
  gap: 15rpx;
  margin-bottom: 15rpx;
}

.form-row:last-child {
  margin-bottom: 0;
}

.input {
  flex: 1;
  padding: 15rpx;
  border: 1rpx solid #ddd;
  border-radius: 6rpx;
  font-size: 28rpx;
  background: white;
}

.btn-query {
  background: #34c759;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
}

.results {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.result-list {
  height: 400rpx;
}

.result-item {
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
}

.result-item:last-child {
  margin-bottom: 0;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.asset-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
}

.asset-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.asset-details {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.detail-item {
  font-size: 26rpx;
  color: #666;
}

.logs {
  background: white;
  border-radius: 10rpx;
  padding: 20rpx;
}

.log-container {
  height: 300rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  padding: 15rpx;
}

.log-item {
  margin-bottom: 10rpx;
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-text {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}
</style>
