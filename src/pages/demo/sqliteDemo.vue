<template>
  <view class="sqlite-demo">
    <view class="header">
      <text class="title">SQLite 资产数据库演示</text>
    </view>
    
    <view class="section">
      <view class="section-title">数据库操作</view>
      <button @click="initDatabase" :loading="loading" class="btn">初始化数据库</button>
      <button @click="testQuery" :loading="loading" class="btn">测试查询功能</button>
      <button @click="clearDatabase" :loading="loading" class="btn btn-danger">清空数据库</button>
    </view>
    
    <view class="section">
      <view class="section-title">查询操作</view>
      <view class="query-form">
        <view class="form-item">
          <text class="label">起始编号:</text>
          <input v-model="queryParams.startNumber" placeholder="如: ASSET-20250101-001" class="input" />
        </view>
        <view class="form-item">
          <text class="label">结束编号:</text>
          <input v-model="queryParams.endNumber" placeholder="如: ASSET-20250101-010" class="input" />
        </view>
        <view class="form-item">
          <text class="label">限制条数:</text>
          <input v-model.number="queryParams.limit" type="number" placeholder="如: 10" class="input" />
        </view>
        <button @click="queryByRange" :loading="loading" class="btn btn-primary">范围查询</button>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">精确查询</view>
      <view class="query-form">
        <view class="form-item">
          <text class="label">资产编号:</text>
          <input v-model="exactQueryNumber" placeholder="如: ASSET-20250101-001" class="input" />
        </view>
        <button @click="queryByExactNumber" :loading="loading" class="btn btn-primary">精确查询</button>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">统计信息</view>
      <button @click="getStatistics" :loading="loading" class="btn">获取统计信息</button>
      <view v-if="statistics" class="statistics">
        <text>总资产数量: {{ statistics.totalCount }}</text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">查询结果</view>
      <view v-if="queryResults.length > 0" class="results">
        <view v-for="(asset, index) in queryResults" :key="asset.id" class="asset-item">
          <view class="asset-header">
            <text class="asset-number">{{ asset.number }}</text>
            <text class="asset-name">{{ asset.name }}</text>
          </view>
          <view class="asset-details">
            <text class="detail-item">型号: {{ asset.model }}</text>
            <text class="detail-item">部门: {{ asset.department }}</text>
            <text class="detail-item">状态: {{ asset.status }}</text>
            <text class="detail-item">创建时间: {{ asset.createTime }}</text>
          </view>
        </view>
      </view>
      <view v-else-if="!loading" class="no-data">
        <text>暂无数据</text>
      </view>
    </view>
    
    <view class="section">
      <view class="section-title">日志</view>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text>{{ log }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { sqliteManager, AssetData } from '@/utils/sqlite'
import { initAssetDatabase, testQueryFunctions } from '@/utils/assetDataGenerator'

// 响应式数据
const loading = ref(false)
const queryResults = ref<AssetData[]>([])
const statistics = ref<{ totalCount: number } | null>(null)
const logs = ref<string[]>([])
const exactQueryNumber = ref('')

// 查询参数
const queryParams = reactive({
  startNumber: '',
  endNumber: '',
  limit: 10
})

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 初始化数据库
const initDatabase = async () => {
  loading.value = true
  try {
    addLog('开始初始化数据库...')
    await initAssetDatabase()
    addLog('数据库初始化成功')
    await getStatistics()
  } catch (error) {
    addLog(`数据库初始化失败: ${error}`)
    uni.showToast({
      title: '初始化失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 测试查询功能
const testQuery = async () => {
  loading.value = true
  try {
    addLog('开始测试查询功能...')
    await testQueryFunctions()
    addLog('查询功能测试完成')
    uni.showToast({
      title: '测试完成',
      icon: 'success'
    })
  } catch (error) {
    addLog(`查询测试失败: ${error}`)
    uni.showToast({
      title: '测试失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 清空数据库
const clearDatabase = async () => {
  loading.value = true
  try {
    addLog('开始清空数据库...')
    await sqliteManager.clearAllAssets()
    addLog('数据库已清空')
    queryResults.value = []
    statistics.value = null
    uni.showToast({
      title: '清空成功',
      icon: 'success'
    })
  } catch (error) {
    addLog(`清空数据库失败: ${error}`)
    uni.showToast({
      title: '清空失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 范围查询
const queryByRange = async () => {
  loading.value = true
  try {
    addLog(`开始范围查询: ${queryParams.startNumber || '无'} 到 ${queryParams.endNumber || '无'}`)
    const results = await sqliteManager.queryAssetsByNumberRange({
      startNumber: queryParams.startNumber || undefined,
      endNumber: queryParams.endNumber || undefined,
      limit: queryParams.limit || undefined
    })
    queryResults.value = results
    addLog(`范围查询完成，找到 ${results.length} 条数据`)
  } catch (error) {
    addLog(`范围查询失败: ${error}`)
    uni.showToast({
      title: '查询失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 精确查询
const queryByExactNumber = async () => {
  if (!exactQueryNumber.value.trim()) {
    uni.showToast({
      title: '请输入资产编号',
      icon: 'none'
    })
    return
  }
  
  loading.value = true
  try {
    addLog(`开始精确查询: ${exactQueryNumber.value}`)
    const result = await sqliteManager.queryAssetByNumber(exactQueryNumber.value)
    if (result) {
      queryResults.value = [result]
      addLog(`精确查询完成，找到资产: ${result.name}`)
    } else {
      queryResults.value = []
      addLog(`精确查询完成，未找到编号为 ${exactQueryNumber.value} 的资产`)
    }
  } catch (error) {
    addLog(`精确查询失败: ${error}`)
    uni.showToast({
      title: '查询失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const getStatistics = async () => {
  loading.value = true
  try {
    addLog('开始获取统计信息...')
    const totalCount = await sqliteManager.getAssetCount()
    statistics.value = { totalCount }
    addLog(`统计信息获取完成，总资产数量: ${totalCount}`)
  } catch (error) {
    addLog(`获取统计信息失败: ${error}`)
    uni.showToast({
      title: '获取统计失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 页面加载时初始化数据库
onMounted(async () => {
  try {
    await sqliteManager.initDatabase()
    addLog('数据库连接已建立')
  } catch (error) {
    addLog(`数据库连接失败: ${error}`)
  }
})
</script>

<style scoped>
.sqlite-demo {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 20rpx;
}

.btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  margin: 10rpx;
  font-size: 28rpx;
}

.btn-primary {
  background-color: #34c759;
}

.btn-danger {
  background-color: #ff3b30;
}

.query-form {
  margin-top: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #666;
}

.input {
  flex: 1;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.statistics {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.results {
  margin-top: 20rpx;
}

.asset-item {
  border: 2rpx solid #eee;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #fafafa;
}

.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.asset-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
}

.asset-name {
  font-size: 28rpx;
  color: #333;
}

.asset-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  font-size: 24rpx;
  color: #666;
}

.no-data {
  text-align: center;
  padding: 60rpx;
  color: #999;
  font-size: 28rpx;
}

.log-container {
  height: 400rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
}

.log-item {
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
</style>
