<route lang="json5" type="page">
{
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "资产盘点"
  }
}
</route>

<template>
	<view class="detail-container">
		<nav-bar>{{ isReadonly ? '盘点详情' : '资产盘点' }}</nav-bar>
		<uni-fab
			v-if="!isReadonly"
			:pattern="{
				color: '#7A7E83',
				backgroundColor: '#fff',
				selectedColor: '#007AFF',
				buttonColor: '#007AFF',
				iconColor: '#fff'
			}"
			:content="[
				{
					iconPath: '/static/uploadImg.png',
					selectedIconPath: '',
					text: '上传结果',
					active: false
				},
				{
					iconPath: '/static/close.png',
					selectedIconPath: '',
					text: '结束盘点',
					active: false
				}
			]"
			horizontal="right"
			vertical="bottom"
			direction="vertical"
			@trigger="trigger"
		>
		</uni-fab>

		<!-- 盘点信息卡片 -->
		<view class="info-section">
			<view class="info-card">
				<view class="info-item">
					<view class="info-label">盘点名称</view>
					<view class="info-value">{{ detailData.name || '未命名盘点' }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">任务状态</view>
					<view class="info-value">
						<view class="status-badge" :class="getStatusClass(detailData.status)">
							{{ getStatusName(detailData.status)  || '未知状态' }}
						</view>
					</view>
				</view>
				<view class="info-item">
					<view class="info-label">盘点人</view>
					<view class="info-value">{{ detailData.userId_dictText || '未分配' }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">盘点总数</view>
					<view class="info-value highlight">{{ detailData.totalCount || 0 }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">待盘点数</view>
					<view class="info-value pending">{{ detailData.pendingCount || 0 }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">已盘点数</view>
					<view class="info-value completed">{{ detailData.completedCount || 0 }}</view>
				</view>
				<view class="info-item">
					<view class="info-label">创建时间</view>
					<view class="info-value time">{{ formatDateTime(detailData.createTime) }}</view>
				</view>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="action-buttons" v-if="shouldShowButton && !isReadonly">
			<button 
				class="btn btn-primary" 
				:class="{ 'btn-disabled': isScanning }"
				:disabled="isScanning"
				@click="handleScan"
			>
				{{ isScanning ? '正在扫码...' : '扫码盘点' }}
			</button>
			<button 
				v-if="isScanning"
				class="btn btn-secondary" 
				@click="stopScan"
			>
				停止扫码
			</button>
		</view>

		<!-- RFID盘点按钮 -->
		<view class="rfid-buttons" v-if="shouldShowButton && !isReadonly">
			<button 
				class="btn btn-rfid" 
				:class="{ 'btn-disabled': isInventoryRunning }"
				:disabled="isInventoryRunning"
				@click="handleRfidInventory"
			>
				{{ isInventoryRunning ? '盘点中...' : '开始RFID盘点' }}
			</button>
			<button 
				v-if="isInventoryRunning"
				class="btn btn-rfid-stop" 
				@click="stopRfidInventory"
			>
				停止盘点
			</button>
		</view>

		<!-- 状态信息 -->
		<!-- <view class="status-info" v-if="resultMessage || isRfidInitialized">
			<text class="status-text">{{ resultMessage }}</text>
			<text v-if="isRfidInitialized" class="status-text rfid-status">RFID已初始化</text>
		</view> -->

		<!-- 操作工具 -->
		<!-- <view class="tool-buttons" v-if="shouldShowButton && !isReadonly && (scannedTags.length > 0 || completedAssets.length > 0 || surplusAssets.length > 0)">
			<button class="btn btn-tool" @click="clearScanData">
				清空数据
			</button>
			<button class="btn btn-tool" @click="clearScannedTags">
				清空标签
			</button>
			<button class="btn btn-tool" @click="exportInventoryResult">
				导出结果
			</button>
		</view> -->

		<Tabs :tabList="tabList" :activeColor="'#007aff'" @change="handleTabChange">
			<template #tab-0>
				<view class="tab-content">
					<view v-if="pendingAssets.length === 0" class="empty-state">
						<text class="empty-text">暂无待盘点资产</text>
					</view>
					<view v-else v-for="asset in pendingAssets" :key="asset.id" class="content-item">
						<!-- <view v-else v-for="asset in pendingAssets" :key="asset.id" class="content-item" @click="markAsCompleted(asset)"></view> -->
						<view class="asset-info">
							<view class="asset-image" v-if="asset.imageUrl">
								<image :src="asset.imageUrl" class="asset-img"/>
							</view>
							<view class="asset-details">
								<view class="asset-name">{{ asset.assetName }}</view>
								<view class="asset-location">{{ asset.assetNumber }}</view>
							</view>
						</view>
					</view>
				</view>
			</template>
			<template #tab-1>
				<view class="tab-content">
					<view v-if="completedAssets.length === 0" class="empty-state">
						<text class="empty-text">暂无已盘点资产</text>
					</view>
					<view v-else v-for="asset in completedAssets" :key="asset.id" class="content-item">
						<view class="asset-info">
							<view class="asset-image" v-if="asset.imageUrl">
								<image :src="asset.imageUrl" class="asset-img"/>
							</view>
							<view class="asset-details">
								<view class="asset-name">
									{{ asset.assetName || asset.name }}
								</view>
								<view class="asset-location">{{ asset.assetNumber || asset.location }}</view>
								<view v-if="asset.inventoryTime" class="asset-time">
									盘点时间: {{ formatDateTime(asset.inventoryTime) }}
								</view>
							</view>
						</view>
						<view class="asset-status" :class="asset.isSurplus ? 'surplus' : 'completed'">
							{{ asset.isSurplus ? '盘盈' : '已盘点' }}
						</view>
					</view>
				</view>
			</template>
		</Tabs>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { http } from "@/utils/http"
import Tabs from '@/components/Tabs/index.vue'
import uhfManager from '@/utils/uhf-library.js'
import uniFab from '@/uni_modules/uni-fab/components/uni-fab/uni-fab.vue'
import IconFont from '@/components/IconFont/IconFont.vue'

// 定义接口类型
interface InventoryDetail {
	id: number
	name: string
	status: string
	userId: string
	totalCount: number
	pendingCount: number
	completedCount: number
	entryStartDate: string | null
	entryEndDate: string | null
	categoryCode: string | null
	storageLocation: string
	createTime: string
	status_dictText?: string
	userId_dictText?: string
}

// 响应式数据
const detailData = ref<InventoryDetail>({} as InventoryDetail)

// 只读模式
const isReadonly = ref(false)

// 扫码相关状态
const scanInitialized = ref(false)
const isScanning = ref(false)
const scanError = ref('')

// RFID相关状态
const isInventoryRunning = ref(false)
const isRfidInitialized = ref(false)
const resultMessage = ref('')

// 标签数据存储
const scannedTags = ref<any[]>([])
const matchedAssets = ref<any[]>([])
const filteredCount = ref(0) // 过滤的脏数据数量

// 状态映射
const statusMap = {
	'0': '待开始',
	'1': '盘点中', 
	'2': '已结束',
	'3': '已取消'
} as const

const statusClassMap = {
	'0': 'status-pending',
	'1': 'status-progress', 
	'2': 'status-completed',
	'3': 'status-cancelled'
} as const

const buttonTextMap = {
	'0': '开始盘点',
	'1': '继续盘点'
} as const

const tabList = ref([
	{
		text: '待盘点',
		navTarget: '待盘点资产列表',
		key: 'pending'
	},
	{
		text: '已盘点',
		navTarget: '已盘点资产列表',
		key: 'completed'
	},
])
const result = ref('')

// 模拟资产数据
const assetList = ref([])

// 计算属性：根据状态筛选资产
const pendingAssets = computed(() => {
	return assetList.value.filter(asset => asset.inventoryFlag === '0')
})

const completedAssets = computed(() => {
	return assetList.value.filter(asset => asset.inventoryFlag === '1' || asset.inventoryFlag === '2')
})

const surplusAssets = computed(() => {
	return assetList.value.filter(asset => asset.inventoryFlag === '2')
})

const allCompletedAssets = computed(() => {
	return assetList.value.filter(asset => asset.inventoryFlag !== '0')
})

// 计算属性
const shouldShowButton = computed(() => {
	return detailData.value.status === '0' || detailData.value.status === '1'
})

// 页面加载
onLoad((option: any) => {
	console.log(option)
	
	// 检查是否为只读模式
	if (option.readonly === 'true') {
		isReadonly.value = true
		console.log('进入只读模式')
	}
	
	if (option.id) {
		getInventoryDetail(option.id)
		getInventoryList(option.id)
	}
	// 设置RFID标签数据监听
	var globalEvent = uni.requireNativePlugin('globalEvent');
	globalEvent.addEventListener('uhf-tag-data', function(e) {
		console.log('收到UHF标签数据:', JSON.stringify(e));
		
		// 处理接收到的RFID标签数据
		if (e && e.tags && Array.isArray(e.tags)) {
			// 获取所有标签数据，并添加扫描时间
			const newTags = e.tags.map((tag: any) => ({
				...tag,
				scanTime: new Date().toISOString(),
				id: tag.epc || tag.tid || Math.random().toString(36).substr(2, 9)
			}));
			
			console.log('新接收到的标签数据:', newTags);
			console.log('当前已扫描的标签数量:', scannedTags.value.length);
			
			// 先过滤脏数据：只保留assetNumber包含'-'字符的标签
			const validTags = filterValidTags(newTags);
			
			// 去重处理：检查新标签中是否有重复的EPC，以及与已扫描标签的重复
			const uniqueNewTags = deduplicateTagsGlobally(validTags);
			
			console.log('去重后的新标签数量:', uniqueNewTags.length);
			
			// 添加到扫描标签数组
			scannedTags.value = [...scannedTags.value, ...uniqueNewTags];
			
			// 处理标签数据
			handleRfidTags(uniqueNewTags);
		}
	});
})

// 页面卸载时清理
onUnload(() => {
	console.log('Page Unload')
	
	// 页面卸载时停止盘点
	if (isInventoryRunning.value) {
		stopRfidInventory()
	}
})

// 页面卸载时清理
onUnmounted(() => {
	// 如果正在扫码，停止扫码
	if (isScanning.value) {
		stopScan()
	}
	if (isInventoryRunning.value) {
		stopRfidInventory()
	}
	
	// 移除事件监听
	uhfManager.removeInventoryListener()
})

// 获取盘点详情
const getInventoryDetail = async (id: string) => {
	try {
		const res: any = await http.get('/jeecg/assetInventoryPlans/queryById', { id })
		console.log(res)
		if (res.result) {
			detailData.value = res.result
		}
	} catch (err) {
		console.error('获取盘点详情失败:', err)
		uni.showToast({
			title: '获取数据失败',
			icon: 'none'
		})
	}
}

// 获取盘点列表
const getInventoryList = async (id: string) => {
	try {
		const res: any = await http.get('/jeecg/assetInventoryPlans/queryAssetInventoryDetailsByMainId', { id })
		assetList.value = res.result
		console.log(res, '盘点列表')
	} catch (err) {
		console.error('获取盘点列表失败:', err)
	}
}

// 处理tab切换
const handleTabChange = (index: number, item: any) => {
	console.log('切换到tab:', index, item)
	// 这里可以处理tab切换的逻辑
	// 比如加载不同的数据、更新状态等
}

// 标记资产为已完成
// const markAsCompleted = (asset: any) => {
// 	uni.showModal({
// 		title: '确认盘点',
// 		content: `确定要将"${asset.assetNumber}"标记为已盘点吗？`,
// 		success: (res) => {
// 			if (res.confirm) {
// 				// 更新资产状态
// 				const assetIndex = assetList.value.findIndex(item => item.id === asset.id)
// 				if (assetIndex !== -1) {
// 					assetList.value[assetIndex].status = 'completed'
					
// 					// 更新盘点统计数据
// 					detailData.value.completedCount += 1
// 					detailData.value.pendingCount -= 1
					
// 					uni.showToast({
// 						title: '盘点完成',
// 						icon: 'success'
// 					})
// 				}
// 			}
// 		}
// 	})
// }

// 初始化扫码模块
const initScan = (): Promise<void> => {
	return new Promise((resolve, reject) => {
		if (scanInitialized.value) {
			resolve()
			return
		}

		try {
			const scan = uni.requireNativePlugin('ScanModule')
			scan.init({ mode: 3 }, (res: any) => {
				console.log('扫码模块初始化结果:', res)
				if (res && res.success !== false) {
					scanInitialized.value = true
					scanError.value = ''
					uni.showToast({ 
						title: '扫码模块初始化成功', 
						icon: 'success',
						duration: 1500
					})
					resolve()
				} else {
					scanError.value = res?.message || '初始化失败'
					uni.showToast({ 
						title: '扫码模块初始化失败', 
						icon: 'error',
						duration: 2000
					})
					reject(new Error(scanError.value))
				}
			})
		} catch (error) {
			scanError.value = '无法加载扫码模块'
			console.error('扫码模块初始化异常:', error)
			uni.showToast({ 
				title: '扫码模块加载失败', 
				icon: 'error',
				duration: 2000
			})
			reject(error)
		}
	})
}

// 开始扫码
const startScan = (): Promise<void> => {
	return new Promise((resolve, reject) => {
		if (!scanInitialized.value) {
			reject(new Error('扫码模块未初始化'))
			return
		}

		if (isScanning.value) {
			uni.showToast({ 
				title: '正在扫码中...', 
				icon: 'none',
				duration: 1500
			})
			return
		}

		try {
			isScanning.value = true
			const scan = uni.requireNativePlugin('ScanModule')
			scan.startScan({ timeout: 60, type: 1 }, (res: any) => {
				isScanning.value = false
				result.value = JSON.stringify(res)
				console.log('扫码结果:', result.value)
				
				// 处理扫码结果
				
				handleScanResult(res.result)
			
				resolve()
			}, (err: any) => {
				isScanning.value = false
				console.error('扫码失败:', err)
				uni.showModal({ 
					title: '扫码失败',
					content: '扫码失败：' + JSON.stringify(err),
					showCancel: false
				})
				reject(err)
			})
		} catch (error) {
			isScanning.value = false
			console.error('扫码异常:', error)
			uni.showToast({ 
				title: '扫码功能异常', 
				icon: 'error',
				duration: 2000
			})
			reject(error)
		}
	})
}

// 停止扫码
const stopScan = () => {
	try {
		if (isScanning.value) {
			const scan = uni.requireNativePlugin('ScanModule')
			scan.stopScan()
			isScanning.value = false
			result.value = '已停止'
			uni.showToast({ 
				title: '已停止扫码', 
				icon: 'success',
				duration: 1500
			})
		}
	} catch (error) {
		console.error('停止扫码异常:', error)
		uni.showToast({ 
			title: '停止扫码失败', 
			icon: 'error',
			duration: 1500
		})
	}
}

// 处理扫码结果
const handleScanResult = (scanResult: string) => {
	console.log('处理扫码结果:', scanResult)
	
	// 这里可以根据扫码结果进行相应的业务处理
	// 比如查找对应的资产、更新盘点状态等
	uni.showToast({ 
		title: '扫码成功', 
		icon: 'success',
		duration: 1500
	})
	stopScan()
	// TODO: 根据扫码结果处理业务逻辑
	// 例如：查找资产、更新盘点状态等
}
// 过滤脏数据：只保留assetNumber包含'-'字符的标签
const filterValidTags = (tags: any[]) => {
	const validTags = tags.filter(tag => {
		const assetNumber = tag.assetNumber || ''
		const hasValidFormat = assetNumber.includes('-')
		
		if (!hasValidFormat) {
			console.log('过滤脏数据:', {
				epc: tag.epc,
				assetNumber: assetNumber,
				reason: 'assetNumber不包含"-"字符'
			})
		}
		
		return hasValidFormat
	})
	
	const filteredNum = tags.length - validTags.length
	filteredCount.value += filteredNum
	
	console.log(`数据过滤结果: 原始${tags.length}个标签, 有效${validTags.length}个标签, 过滤${filteredNum}个脏数据`)
	
	return validTags
}

// 全局去重标签数据：检查新标签与已扫描标签的重复
const deduplicateTagsGlobally = (newTags: any[]) => {
	// 获取已扫描标签的所有EPC
	const existingEpcs = new Set(scannedTags.value.map(tag => tag.epc));
	
	console.log('已存在的EPC列表:', Array.from(existingEpcs));
	
	// 过滤新标签，只保留未扫描过的EPC
	const uniqueTags = newTags.filter(tag => {
		const epc = tag.epc;
		if (!epc) {
			console.log('标签缺少EPC字段:', tag);
			return false;
		}
		
		if (existingEpcs.has(epc)) {
			console.log('EPC已存在，跳过:', epc);
			return false;
		}
		
		console.log('新的EPC，添加:', epc);
		return true;
	});
	
	// 在新标签内部也进行去重
	const seenEpc = new Set();
	return uniqueTags.filter(tag => {
		const epc = tag.epc;
		if (seenEpc.has(epc)) {
			console.log('新标签内部重复EPC，跳过:', epc);
			return false;
		}
		seenEpc.add(epc);
		return true;
	});
}

// 处理RFID标签数据
const handleRfidTags = (tags: any[]) => {
	console.log('处理RFID标签数据:', tags)
	
	// 遍历每个标签
	tags.forEach(tag => {
		if (tag && (tag.epc || tag.tid)) {
			// 查找匹配的待盘点资产
			const matchedAsset = findMatchingAsset(tag)
			
			if (matchedAsset) {
				// 找到匹配的资产，移动到已盘点
				moveAssetToCompleted(matchedAsset, tag)
			} else {
				// 未找到匹配的资产，作为盘盈处理
				addSurplusAsset(tag)
			}
		}
	})
	
	// 更新盘点统计
	updateInventoryStats()
}

// 查找匹配的待盘点资产
const findMatchingAsset = (tag: any) => {
	const tagEpc = tag.epc || ''
	
	// 在待盘点资产中查找匹配的assetEpc
	return pendingAssets.value.find(asset => {
		return asset.assetEpc === tagEpc
	})
}

// 将资产移动到已盘点
const moveAssetToCompleted = (asset: any, tag: any) => {
	console.log('移动资产到已盘点:', asset, tag)
	
	// 从待盘点列表中删除该资产
	const pendingIndex = assetList.value.findIndex(item => item.id === asset.id)
	if (pendingIndex !== -1) {
		// 从原数组中删除
		const movedAsset = assetList.value.splice(pendingIndex, 1)[0]
		
		// 更新资产状态为已盘点
		movedAsset.inventoryFlag = '1'
		movedAsset.inventoryTime = new Date().toISOString()
		movedAsset.rfidData = tag
		
		// 添加到已盘点列表（重新添加到数组末尾）
		assetList.value.push(movedAsset)
		
		// 添加到已匹配资产列表
		matchedAssets.value.push(movedAsset)
		
		uni.showToast({
			title: `已盘点: ${asset.assetName}`,
			icon: 'success',
			duration: 1500
		})
	}
}

// 添加盘盈资产
const addSurplusAsset = (tag: any) => {
	console.log('添加盘盈资产:', tag)
	
	const surplusAsset = {
		id: `surplus_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
		assetName: '',
		assetNumber:'ASSET-' + tag.assetNumber || '',
		assetEpc: tag.epc || '',
		inventoryFlag: '2', // 盘盈
		inventoryTime: new Date().toISOString(),
		rfidData: tag,
		isSurplus: true // 盘盈标识
	}
	
	// 添加到已盘点列表（盘盈也属于已盘点）
	assetList.value.push(surplusAsset)
	
	// 添加到已匹配资产列表
	matchedAssets.value.push(surplusAsset)
	
	uni.showToast({
		title: `盘盈: ${surplusAsset.assetName}`,
		icon: 'none',
		duration: 1500
	})
}

// 更新盘点统计
const updateInventoryStats = () => {
	// 重新计算统计数据
	const pending = assetList.value.filter(asset => asset.inventoryFlag === '0').length
	const completed = assetList.value.filter(asset => asset.inventoryFlag === '1').length
	const surplus = assetList.value.filter(asset => asset.isSurplus === true).length
	
	// 更新详情数据
	detailData.value.pendingCount = pending
	detailData.value.completedCount = completed
	
	// 更新结果消息
	resultMessage.value = `已扫描: ${scannedTags.value.length}个标签, 已盘点: ${completed}个, 其中盘盈: ${surplus}个, 过滤: ${filteredCount.value}个脏数据`
}

// 清空已扫描标签
const clearScannedTags = () => {
	uni.showModal({
		title: '确认清空',
		content: '确定要清空已扫描的标签数据吗？此操作不可恢复。',
		success: (res) => {
			if (res.confirm) {
				scannedTags.value = []
				
				// 更新统计
				updateInventoryStats()
				
				uni.showToast({
					title: '标签已清空',
					icon: 'success'
				})
			}
		}
	})
}

// 清空扫描数据
const clearScanData = () => {
	uni.showModal({
		title: '确认清空',
		content: '确定要清空所有扫描数据吗？此操作不可恢复。',
		success: (res) => {
			if (res.confirm) {
				scannedTags.value = []
				matchedAssets.value = []
				filteredCount.value = 0
				
				// 重置RFID状态
				isRfidInitialized.value = false
				isInventoryRunning.value = false
				
				// 重置所有资产状态为待盘点
				assetList.value.forEach(asset => {
					if (asset.inventoryFlag !== '0') {
						asset.inventoryFlag = '0'
						if (asset.inventoryTime) {
							delete asset.inventoryTime
						}
						if (asset.rfidData) {
							delete asset.rfidData
						}
					}
				})
				
				// 更新统计
				updateInventoryStats()
				
				uni.showToast({
					title: '数据已清空',
					icon: 'success'
				})
			}
		}
	})
}

// 导出盘点结果
const exportInventoryResult = () => {
	const result = {
		inventoryId: detailData.value.id,
		inventoryName: detailData.value.name,
		exportTime: new Date().toISOString(),
		statistics: {
			totalScanned: scannedTags.value.length,
			completed: completedAssets.value.length,
			surplus: surplusAssets.value.length,
			pending: pendingAssets.value.length
		},
		scannedTags: scannedTags.value,
		completedAssets: completedAssets.value,
		surplusAssets: surplusAssets.value
	}
	
	console.log('盘点结果:', result)
	
	// 这里可以实现导出功能，比如保存到文件或发送到服务器
	uni.showModal({
		title: '导出成功',
		content: `盘点结果已生成，共扫描${scannedTags.value.length}个标签，盘点${completedAssets.value.length}个资产，发现${surplusAssets.value.length}个盘盈。`,
		showCancel: false
	})
}

// 处理RFID盘点（自动初始化+开始盘点）
const handleRfidInventory = async () => {
	try {
		// 如果未初始化，先进行初始化
		if (!isRfidInitialized.value) {
			uni.showLoading({ 
				title: '正在初始化RFID...',
				mask: true
			})
			
			await initializeRfidUHF()
			
			uni.hideLoading()
		}
		
		// 开始盘点
		await startRfidInventory()
		
	} catch (error) {
		uni.hideLoading()
		console.error('RFID盘点失败:', error)
		uni.showModal({
			title: '盘点失败',
			content: error instanceof Error ? error.message : 'RFID盘点功能异常，请重试',
			showCancel: false,
			confirmText: '确定'
		})
	}
}

const initializeRfidUHF = async () => {
	try {
		resultMessage.value = '初始化中...'
		const rfidResult = await uhfManager.initialize({
			readPower: 30,
			writePower: 30,
			regional: 1
		})
		console.log(rfidResult, 'rfidResult')
		
		if (rfidResult.success !== false) {
			isRfidInitialized.value = true
			resultMessage.value = rfidResult.message || 'RFID初始化完成'
		} else {
			throw new Error(rfidResult.message || '初始化失败')
		}
	} catch (error) {
		isRfidInitialized.value = false
		resultMessage.value = `初始化失败: ${error instanceof Error ? error.message : '未知错误'}`
		console.error('UHF initialization error:', error)
		throw error
	}
}

const startRfidInventory = async () => {
	try {
		resultMessage.value = '开始盘点...'
		const result = await uhfManager.startInventory()
		console.log(result, 'result')
		isInventoryRunning.value = true
		resultMessage.value = result.message || '盘点已开始'
	} catch (error) {
		resultMessage.value = `开始盘点失败: ${error instanceof Error ? error.message : '未知错误'}`
		console.error('Start inventory error:', error)
	}
}
const stopRfidInventory = async () => {
	try {
		resultMessage.value = '停止盘点...'
		const result = await uhfManager.stopInventory()
		
		resultMessage.value = result.message || '盘点已停止'
		if (result.success) {
			isInventoryRunning.value = false
		}
	} catch (error) {
		resultMessage.value = `停止盘点失败: ${error instanceof Error ? error.message : '未知错误'}`
		console.error('Stop inventory error:', error)
	}
}


const trigger = (e: any) => {
	console.log(e, 'e')
	if (e.index === 0) {
		handleUpload()
	} else if (e.index === 1) {
		handleEndInventory()
	}
}

// 处理上传结果
const handleUpload = async () => {
	console.log('开始上传盘点结果')
	
	// 显示加载提示
	uni.showLoading({
		title: '正在上传...',
		mask: true
	})
	
	try {
		// 构建已盘点列表数据
		const completedInventoryDetailsList = allCompletedAssets.value.map(asset => ({
			assetNumber: asset.assetNumber || asset.assetCode || '',
			inventoryFlag: asset.inventoryFlag || '1',
		}))
		
		// 构建待盘点列表数据
		const pendingInventoryDetailsList = pendingAssets.value.map(asset => ({
			assetNumber: asset.assetNumber || asset.assetCode || '',
			inventoryFlag: asset.inventoryFlag || '0',
		}))
		
		// 构建上传参数
		const params = {
			// 已盘点列表
			completedInventoryDetailsList,
			// 待盘点列表  
			pendingInventoryDetailsList,
			// 盘点任务状态
			status: detailData.value.status || '1',
			// 盘点任务ID
			id: detailData.value.id || 0,
		}
		
		console.log('上传参数:', params)
		
		// 发送上传请求
		const res = await http.post('/jeecg/assetInventoryPlans/sync', params)
		
		uni.hideLoading()
		
		if (res.code === 200) {
			uni.showToast({
				title: '上传成功',
				icon: 'success',
				duration: 2000
			})
			
			// 更新任务状态为已结束
			detailData.value.status = '2'
			
			console.log('上传成功:', res)
		} else {
			throw new Error(res.msg || '上传失败')
		}
		
	} catch (error) {
		uni.hideLoading()
		
		console.error('上传失败:', error)
		
		uni.showModal({
			title: '上传失败',
			content: error instanceof Error ? error.message : '网络错误，请重试',
			showCancel: false,
			confirmText: '确定'
		})
	}
}

// 处理结束盘点
const handleEndInventory = () => {
	console.log('结束盘点')
	
	uni.showModal({
		title: '确认结束盘点',
		content: '确定要结束当前盘点任务吗？结束后将无法继续盘点。',
		confirmText: '确定结束',
		cancelText: '取消',
		success: (res) => {
			if (res.confirm) {
				// 停止所有扫描和RFID操作
				stopScan()
				stopRfidInventory()
				
				// 更新任务状态为已结束
				detailData.value.status = '2'
				
				uni.showToast({
					title: '盘点已结束',
					icon: 'success',
					duration: 2000
				})
				
				// 可以在这里添加上传逻辑或跳转到其他页面
				setTimeout(() => {
					// 返回上一页或跳转到盘点列表
					uni.navigateBack()
				}, 2000)
			}
		}
	})
}

// 格式化日期时间
const formatDateTime = (timeStr: string): string => {
	if (!timeStr) return ''
	const date = new Date(timeStr)
	const year = date.getFullYear()
	const month = (date.getMonth() + 1).toString().padStart(2, '0')
	const day = date.getDate().toString().padStart(2, '0')
	const hours = date.getHours().toString().padStart(2, '0')
	const minutes = date.getMinutes().toString().padStart(2, '0')
	const seconds = date.getSeconds().toString().padStart(2, '0')
	return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 获取状态样式类
const getStatusClass = (status: string): string => {
	return statusClassMap[status as keyof typeof statusClassMap] || 'status-default'
}

// 获取状态名称
const getStatusName = (status: string): string => {
	return statusMap[status as keyof typeof statusMap] || '未知状态'
}

// 获取按钮文本
const getButtonText = (status: string): string => {
	return buttonTextMap[status as keyof typeof buttonTextMap] || '操作'
}

// 检查相机权限
const checkCameraPermission = (): Promise<boolean> => {
	return new Promise((resolve) => {
		// #ifdef APP-PLUS
		plus.android.requestPermissions(
			['android.permission.CAMERA'],
			(result: any) => {
				console.log('相机权限检查结果:', result)
				resolve(result.granted && result.granted.length > 0)
			},
			(error: any) => {
				console.error('权限检查失败:', error)
				resolve(false)
			}
		)
		// #endif
		
		// #ifndef APP-PLUS
		// 非APP环境直接返回true
		resolve(true)
		// #endif
	})
}

// 处理开始盘点
const handleScan = async (): Promise<void> => {
	try {
		// 检查相机权限
		const hasPermission = await checkCameraPermission()
		if (!hasPermission) {
			uni.showModal({
				title: '权限不足',
				content: '需要相机权限才能进行扫码，请在设置中开启相机权限',
				showCancel: false,
				confirmText: '确定'
			})
			return
		}

		// 显示加载状态
		uni.showLoading({ 
			title: '正在初始化扫码模块...',
			mask: true
		})

		// 先初始化扫码模块
		await initScan()
		
		// 隐藏加载状态
		uni.hideLoading()
		
		// 开始扫码
		await startScan()
		
		console.log('扫码结果:', result.value)
	} catch (error) {
		// 隐藏加载状态
		uni.hideLoading()
		
		console.error('扫码流程失败:', error)
		
		// 显示错误信息
		uni.showModal({
			title: '扫码失败',
			content: error instanceof Error ? error.message : '扫码功能异常，请重试',
			showCancel: false,
			confirmText: '确定'
		})
	}
}

// 开始盘点
const startInventory = (): void => {
	uni.showToast({
		title: '开始盘点',
		icon: 'success'
	})
	
	// 这里可以跳转到实际的盘点扫描页面
	// uni.navigateTo({
	//   url: `/pages/inventory/scan?id=${detailData.value.id}`
	// })
}
</script>

<style scoped>
.detail-container {
	padding-top: 20rpx;
	padding-bottom: 40rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 信息区块 */
.info-section {
	margin-bottom: 30rpx;
}

.info-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin: 0 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
}

.info-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
	border-bottom: none;
}

.info-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
	min-width: 200rpx;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
	text-align: right;
	word-break: break-all;
}

.info-value.highlight {
	color: #007aff;
	font-weight: 600;
}

.info-value.pending {
	color: #ff9500;
	font-weight: 600;
}

.info-value.completed {
	color: #34c759;
	font-weight: 600;
}

.info-value.time {
	color: #999;
	font-size: 26rpx;
}

/* 状态标签 */
.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	display: inline-block;
}

.status-badge.status-pending {
	background-color: #fff3cd;
	color: #856404;
}

.status-badge.status-progress {
	background-color: #d1ecf1;
	color: #0c5460;
}

.status-badge.status-completed {
	background-color: #d4edda;
	color: #155724;
}

.status-badge.status-cancelled {
	background-color: #f8d7da;
	color: #721c24;
}

.status-badge.status-default {
	background-color: #e2e3e5;
	color: #383d41;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	gap: 20rpx;
	margin: 20rpx;
	padding-bottom: 20rpx;
}

.btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: 500;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.btn-primary {
	background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
	color: white;
	box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);
}

.btn-primary:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(0, 122, 255, 0.4);
}

.btn-secondary {
	background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
	color: white;
	box-shadow: 0 4rpx 20rpx rgba(108, 117, 125, 0.3);
}

.btn-secondary:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(108, 117, 125, 0.4);
}

.btn-disabled {
	opacity: 0.6;
	cursor: not-allowed;
}

.btn-disabled:active {
	transform: none;
	box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);
}

/* RFID按钮样式 */
.rfid-buttons {
	display: flex;
	gap: 15rpx;
	margin: 0 20rpx 20rpx;
	padding-bottom: 20rpx;
	flex-wrap: wrap;
}

.btn-rfid {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	color: white;
	box-shadow: 0 4rpx 20rpx rgba(40, 167, 69, 0.3);
	flex: 1;
	min-width: 200rpx;
}

.btn-rfid:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(40, 167, 69, 0.4);
}

.btn-rfid-stop {
	background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
	color: white;
	box-shadow: 0 4rpx 20rpx rgba(220, 53, 69, 0.3);
	flex: 1;
	min-width: 200rpx;
}

.btn-rfid-stop:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(220, 53, 69, 0.4);
}

/* 状态信息样式 */
.status-info {
	margin: 0 20rpx 20rpx;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.status-text {
	font-size: 28rpx;
	color: #333;
	text-align: center;
	display: block;
}

.rfid-status {
	color: #28a745;
	font-weight: 600;
	margin-top: 10rpx;
}

/* 工具按钮样式 */
.tool-buttons {
	display: flex;
	gap: 15rpx;
	margin: 0 20rpx 20rpx;
	padding-bottom: 20rpx;
	flex-wrap: wrap;
}

.btn-tool {
	background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
	color: white;
	box-shadow: 0 4rpx 20rpx rgba(111, 66, 193, 0.3);
	flex: 1;
	height: 70rpx;
	font-size: 28rpx;
}

.btn-tool:active {
	transform: scale(0.98);
	box-shadow: 0 2rpx 10rpx rgba(111, 66, 193, 0.4);
}

/* 只读模式提示 */
.readonly-tip {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	margin: 20rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	text-align: center;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.tip-text {
	color: #fff;
	font-size: 28rpx;
	font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.detail-container {
		padding: 15rpx;
	}
	
	.info-card {
		padding: 25rpx;
	}
	
	.info-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 10rpx;
	}
	
	.info-value {
		text-align: left;
	}
	
	/* 按钮响应式 */
	.action-buttons,
	.rfid-buttons,
	.tool-buttons {
		flex-direction: column;
		gap: 15rpx;
	}
	
	.btn,
	.btn-rfid,
	.btn-tool {
		width: 100%;
		min-width: auto;
	}
	
	/* 资产信息响应式 */
	.asset-info {
		flex-direction: column;
		align-items: flex-start;
		gap: 15rpx;
	}
	
	.asset-image {
		width: 80rpx;
		height: 80rpx;
		align-self: center;
		margin-right: 20rpx;
	}
	
	.asset-details {
		width: 100%;
	}
}

/* Tab内容样式 */
.tab-content {
	padding: 20rpx;
}

.content-item {
	padding: 24rpx;
	margin-bottom: 16rpx;
	background: #ffffff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.asset-info {
	flex: 1;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
}

.asset-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
	line-height: 1.4;
}

.asset-location {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
}

.asset-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}

.asset-image {
	width: 100rpx;
	height: 100rpx;
	flex-shrink: 0;
}

.asset-img {
	width: 100%;
	height: 100%;
	border-radius: 8rpx;
	object-fit: cover;
}

.asset-status {
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	font-size: 24rpx;
	font-weight: 500;
}

.asset-status.pending {
	background-color: #fff3cd;
	color: #856404;
}

.asset-status.completed {
	background-color: #d4edda;
	color: #155724;
}

.asset-status.surplus {
	background-color: #fff3cd;
	color: #856404;
}

.asset-time {
	font-size: 24rpx;
	color: #999;
	margin-top: 8rpx;
}

.surplus-badge {
	background-color: #ff9500;
	color: white;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	margin-left: 10rpx;
}

.empty-state {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 200rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}
</style>
