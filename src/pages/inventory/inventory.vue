<route lang="json5" type="page">
{
  style: {
    navigationStyle: "custom",
    navigationBarTitleText: "盘点列表"
  }
}
</route>

<template>
  <view class="content">
    <nav-bar>盘点列表</nav-bar>

    <!-- 数据库操作区域 -->
    <view class="db-operations">
      <view class="operation-title">数据库操作</view>
      <view class="operation-buttons">
        <button @click="testSQLite" class="db-btn">测试SQLite模块</button>
        <button @click="getAllList" class="db-btn">同步数据到本地</button>
        <button @click="getDatabaseStats" class="db-btn">查看统计信息</button>
      </view>

      <!-- 查询区域 -->
      <view class="query-section">
        <view class="query-title">按编号范围查询</view>
        <view class="query-form">
          <input
            v-model="queryParams.startNumber"
            placeholder="起始编号"
            class="query-input"
          />
          <input
            v-model="queryParams.endNumber"
            placeholder="结束编号"
            class="query-input"
          />
          <input
            v-model.number="queryParams.limit"
            type="number"
            placeholder="限制条数"
            class="query-input"
          />
          <button @click="queryAssetsByRange" class="query-btn">查询</button>
        </view>
      </view>
    </view>

    <!-- 查询结果 -->
    <view v-if="queryResults.length > 0" class="query-results">
      <view class="results-title">查询结果 ({{ queryResults.length }} 条)</view>
      <view class="results-list">
        <view
          v-for="asset in queryResults"
          :key="asset.id"
          class="result-item"
        >
          <view class="asset-info">
            <text class="asset-number">{{ asset.number }}</text>
            <text class="asset-name">{{ asset.name }}</text>
          </view>
          <view class="asset-details">
            <text class="detail">型号: {{ asset.model }}</text>
            <text class="detail">部门: {{ asset.department }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 盘点列表 -->
    <view class="inventory-list">
      <view
        v-for="item in inventoryList"
        :key="item.id"
        class="inventory-item"
        @click="handleItemClick(item)"
      >
        <view class="item-header">
          <view class="item-title">{{ item.name }}</view>
          <view class="header-right">
            <view class="status-badge" :class="getStatusClass(item.status)">
              {{ item.status_dictText }}
            </view>
          </view>
        </view>

        <view class="item-content">
          <view class="info-row">
            <view class="info-item">
              <text class="label">盘点人：</text>
              <text class="value">{{ item.userId_dictText }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">盘点总数：</text>
              <text class="value highlight">{{ item.totalCount }}</text>
            </view>
            <view class="info-item">
              <text class="label">已盘点数：</text>
              <text class="value completed">{{ item.completedCount }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">创建时间：</text>
              <text class="value time">{{ formatTime(item.createTime) }}</text>
            </view>
            <view class="info-item">
              <!-- 操作按钮移到右上角 -->
              <view class="action-buttons" v-if="shouldShowButton(item.status)">
                <button
                  class="action-btn"
                  :class="getButtonClass(item.status)"
                  @click.stop="handleActionClick(item)"
                >
                  {{ getButtonText(item.status) }}
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="inventoryList.length === 0" class="empty-state">
      <image src="/static/index/empty.png" class="empty-icon" mode="aspectFit"></image>
      <text class="empty-text">暂无盘点任务</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
// import uhfManager from '@/utils/uhf-library.js'
import { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import {http} from "@/utils/http";
import { dataManager, AssetData, StorageType } from '@/utils/dataManager';
import { testSQLiteModule, testDatabaseOperations } from '@/utils/sqliteTest';

// 盘点列表数据
const inventoryList = ref([])

// 数据库查询相关
const queryResults = ref<AssetData[]>([])
const queryParams = reactive({
  startNumber: '',
  endNumber: '',
  limit: 10
})

// 等待plus环境准备就绪
const waitForPlus = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // #ifdef APP-PLUS
    if (typeof plus !== 'undefined') {
      resolve()
      return
    }

    // 等待plus环境加载
    let attempts = 0
    const maxAttempts = 50 // 最多等待5秒

    const checkPlus = () => {
      attempts++
      if (typeof plus !== 'undefined') {
        resolve()
      } else if (attempts >= maxAttempts) {
        reject(new Error('plus环境加载超时'))
      } else {
        setTimeout(checkPlus, 100)
      }
    }

    checkPlus()
    // #endif

    // #ifdef H5
    resolve()
    // #endif
  })
}

// 测试SQLite模块
const testSQLite = async () => {
  try {
    console.log('开始测试SQLite模块...')

    // 等待plus环境
    await waitForPlus()

    // 测试SQLite模块
    const moduleOk = testSQLiteModule()
    console.log('SQLite模块测试结果:', moduleOk)

    if (moduleOk) {
      // 测试数据库操作
      const operationsOk = await testDatabaseOperations()
      console.log('数据库操作测试结果:', operationsOk)

      if (operationsOk) {
        uni.showModal({
          title: 'SQLite测试',
          content: 'SQLite模块和数据库操作都正常！',
          showCancel: false
        })
      } else {
        uni.showModal({
          title: 'SQLite测试',
          content: 'SQLite模块可用，但数据库操作失败',
          showCancel: false
        })
      }
    } else {
      uni.showModal({
        title: 'SQLite测试',
        content: 'SQLite模块不可用，请检查manifest.json配置',
        showCancel: false
      })
    }
  } catch (error) {
    console.error('SQLite测试失败:', error)
    uni.showModal({
      title: 'SQLite测试',
      content: `测试失败: ${error.message}`,
      showCancel: false
    })
  }
}

onLoad(() => {
  // initializeUHF()
	getList()
	getAllList()
})

const resultMessage = ref('')
const status = reactive({
  initialized: false,
})

const getList = () => {
  http.get('/jeecg/assetInventoryPlans/list', {
	  order: 'desc',
	  pageNo: 1,
	  pageSize:10
  }).then((res: any)=> {
	  inventoryList.value = res.result?.records || []
  })
}

const getAllList = async () => {
  try {
    // 等待plus环境准备就绪
    console.log('等待plus环境准备就绪...')
    await waitForPlus()
    console.log('plus环境已准备就绪')

    // 初始化数据管理器（自动选择最佳存储方案）
    console.log('开始初始化数据管理器...')
    await dataManager.initDatabase()
    console.log(`数据管理器初始化完成，使用存储类型: ${dataManager.getStorageType()}`)

    // 获取资产数据
    console.log('开始获取资产数据...')
    const res = await http.post('/jeecg/assets/queryAssetListByParam', {})
    console.log(res, 'res')
    const data = (res as any).result

    if (data && Array.isArray(data)) {
      // 转换数据格式以匹配AssetData接口
      const assets: AssetData[] = data.map((item: any) => ({
        id: item.id || 0,
        number: item.number || '',
        name: item.name || '',
        model: item.model || '',
        categoryCode: item.categoryCode || '',
        imageUrl: item.imageUrl || '',
        status: item.status || '0',
        storageLocation: item.storageLocation || '',
        entryDate: item.entryDate || null,
        additionMethod: item.additionMethod || '',
        originalValue: item.originalValue || null,
        serviceLife: item.serviceLife || null,
        expiryYear: item.expiryYear || '',
        department: item.department || '',
        factoryArea: item.factoryArea || '',
        userId: item.userId || '',
        createTime: item.createTime || '',
        version: item.version || '1.0.0'
      }))

      // 批量插入到数据管理器
      await dataManager.insertAssets(assets)

      console.log(`成功存储 ${assets.length} 条资产数据到本地存储 (${dataManager.getStorageType()})`)

      // 显示成功提示
      uni.showToast({
        title: `已存储${assets.length}条数据`,
        icon: 'success',
        duration: 2000
      })

      // 可选：查询验证数据是否存储成功
      const count = await dataManager.getAssetCount()
      console.log(`存储中现有 ${count} 条资产数据`)

    } else {
      console.warn('获取到的数据格式不正确或为空')
      uni.showToast({
        title: '数据格式错误',
        icon: 'error'
      })
    }

  } catch (error) {
    console.error('存储资产数据失败:', error)
    uni.showToast({
      title: '存储失败',
      icon: 'error'
    })
  }
}

// 按编号范围查询资产数据
const queryAssetsByRange = async () => {
  try {
    // 等待plus环境准备就绪
    await waitForPlus()

    // 确保数据管理器已初始化
    await dataManager.initDatabase()

    const results = await dataManager.queryAssetsByNumberRange({
      startNumber: queryParams.startNumber || undefined,
      endNumber: queryParams.endNumber || undefined,
      limit: queryParams.limit || undefined
    })
    queryResults.value = results

    uni.showToast({
      title: `找到${results.length}条数据`,
      icon: 'success'
    })

    console.log('查询结果:', results)
  } catch (error) {
    console.error('查询失败:', error)
    uni.showToast({
      title: '查询失败',
      icon: 'error'
    })
  }
}

// 获取存储统计信息
const getDatabaseStats = async () => {
  try {
    // 等待plus环境准备就绪
    await waitForPlus()

    // 确保数据管理器已初始化
    await dataManager.initDatabase()

    const count = await dataManager.getAssetCount()
    const storageType = dataManager.getStorageType()
    const storageInfo = dataManager.getStorageInfo()

    let content = `当前存储中共有 ${count} 条资产数据\n存储类型: ${storageType}`
    if (storageType === StorageType.LOCALSTORAGE && storageInfo.estimatedSize) {
      content += `\n存储大小: ${storageInfo.estimatedSize}`
    }

    uni.showModal({
      title: '存储统计',
      content,
      showCancel: false
    })
  } catch (error) {
    console.error('获取统计信息失败:', error)
    uni.showToast({
      title: '获取统计失败',
      icon: 'error'
    })
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': 'status-pending',
    '1': 'status-progress',
    '2': 'status-completed',
    '3': 'status-cancelled'
  }
  return statusMap[status] || 'status-default'
}


// 判断是否显示按钮
const shouldShowButton = (status: string) => {
  // 待开始(0)和盘点中(1)显示按钮，盘点结束(2)不显示按钮
  return status === '0' || status === '1'
}

// 获取按钮文本
const getButtonText = (status: string) => {
  const textMap: Record<string, string> = {
    '0': '开始盘点',
    '1': '继续盘点'
  }
  return textMap[status] || '操作'
}

// 获取按钮样式类
const getButtonClass = (status: string) => {
  const classMap: Record<string, string> = {
    '0': 'btn-start',
    '1': 'btn-continue'
  }
  return classMap[status] || 'btn-default'
}

// 处理按钮点击
const handleActionClick = (item: any) => {
  if (item.status === '0') {
    // 开始盘点
    uni.showModal({
      title: '确认开始',
      content: `确定要领取任务并开始盘点吗？`,
      success: (res) => {
        if (res.confirm) {
          startInventory(item)
        }
      }
    })
  } else {
    // 继续盘点
    continueInventory(item)
  }
}

// 开始盘点
const startInventory = (item: any) => {
  // console.log('开始盘点:', item)
  // // 这里可以调用开始盘点的API
  // uni.showToast({
  //   title: '开始盘点',
  //   icon: 'success'
  // })

	http.post('/jeecg/assetInventoryPlans/confirm?id='+ item.id, {
		  id: item.id
	  }).then(res=> {
		  if(res.code === 200) {
			uni.navigateTo({
				url: `/pages/inventory/startInventory/startInventory?id=${item.id}`
			  })
		  }

	  })

  // 跳转到盘点页面

}

// 继续盘点
const continueInventory = (item: any) => {
  console.log('继续盘点:', item)
  // 跳转到盘点页面
	uni.navigateTo({
	  	url: `/pages/inventory/startInventory/startInventory?id=${item.id}`
	})
}

// 处理列表项点击
const handleItemClick = (item: any) => {
  console.log('点击盘点项:', item)

  // 根据盘点状态跳转到不同页面
  if (item.status === '2') {
    // 已结束的盘点，跳转到详情查看页面（只读模式）
    uni.navigateTo({
      url: `/pages/inventory/startInventory/startInventory?id=${item.id}&readonly=true`
    })
  } else {
    // 未结束的盘点，跳转到盘点操作页面
    uni.navigateTo({
      url: `/pages/inventory/startInventory/startInventory?id=${item.id}`
    })
  }
}

// 初始化UHF设备
// async function initializeUHF() {
//   try {
//     resultMessage.value = '初始化中...'
//     const result = await uhfManager.initialize({
//       readPower: 30,
//       writePower: 30,
//       regional: 1,
//     })

//     resultMessage.value = result.message
//     if (result.success) {
//       status.initialized = true
//     }
//   } catch (error) {
//     resultMessage.value = `初始化失败: ${error.message}`
//     console.error('UHF initialization error:', error)
//   }
// }

// // 开始扫码
// async function scanCode() {
//   if (!status.initialized) {
//     resultMessage.value = '请先初始化UHF设备'
//     return
//   }
//   await uhfManager.initializeScan();
// }

// 页面加载时获取盘点列表
onMounted(() => {
  // 这里可以调用API获取盘点列表
  // loadInventoryList()
})
</script>

<style scoped lang="scss">
.content {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

.inventory-list {
  padding: 20rpx;
}

.inventory-item {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  flex: 1;
  margin-right: 20rpx;
}

.header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.status-pending {
    background-color: #fff3cd;
    color: #856404;
  }

  &.status-progress {
    background-color: #d1ecf1;
    color: #0c5460;
  }

  &.status-completed {
    background-color: #d4edda;
    color: #155724;
  }

  &.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.status-default {
    background-color: #e2e3e5;
    color: #383d41;
  }
}

.item-content {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-item {
  display: flex;
  align-items: center;
  // flex: 1;

  &:not(:last-child) {
    margin-right: 20rpx;
  }
}

.label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 8rpx;
}

.value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;

  &.highlight {
    color: #007aff;
    font-weight: 600;
  }

  &.completed {
    color: #34c759;
    font-weight: 600;
  }

  &.time {
    color: #999999;
    font-size: 26rpx;
  }
}

.progress-bar {
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-top: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007aff 0%, #5ac8fa 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  min-width: 120rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  &.btn-start {
    background: #007aff;
    color: #ffffff;

    &:active {
      background: linear-gradient(135deg, #0056cc 0%, #4a9eff 100%);
      transform: scale(0.95);
      box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
    }
  }

  &.btn-continue {
    background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
    color: #ffffff;

    &:active {
      background: linear-gradient(135deg, #28a745 0%, #2bc653 100%);
      transform: scale(0.95);
      box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.2);
    }
  }

  &.btn-default {
    background-color: #f0f0f0;
    color: #666666;

    &:active {
      background-color: #e0e0e0;
      transform: scale(0.95);
      box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 40rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

/* 数据库操作区域样式 */
.db-operations {
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.operation-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #007aff;
  padding-left: 20rpx;
}

.operation-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.db-btn {
  flex: 1;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;

  &:active {
    background-color: #0056cc;
    transform: scale(0.95);
  }
}

.query-section {
  border-top: 2rpx solid #e9ecef;
  padding-top: 30rpx;
}

.query-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.query-form {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.query-input {
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: white;

  &:focus {
    border-color: #007aff;
    outline: none;
  }
}

.query-btn {
  background-color: #34c759;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;

  &:active {
    background-color: #28a745;
    transform: scale(0.95);
  }
}

/* 查询结果样式 */
.query-results {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #34c759;
  padding-left: 20rpx;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.result-item {
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
}

.asset-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.asset-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #007aff;
}

.asset-name {
  font-size: 28rpx;
  color: #333;
}

.asset-details {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.detail {
  font-size: 24rpx;
  color: #666;
}

// 响应式设计
@media (max-width: 750rpx) {
  .inventory-item {
    padding: 24rpx;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .item-title {
    font-size: 30rpx;
    margin-right: 0;
  }

  .header-right {
    align-items: flex-start;
    width: 100%;
  }

  .action-buttons {
    justify-content: flex-start;
  }

  .action-btn {
    min-width: 140rpx;
    font-size: 26rpx;
  }

  .info-row {
    flex-direction: column;

    .info-item {
      margin-bottom: 12rpx;
      margin-right: 0;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
