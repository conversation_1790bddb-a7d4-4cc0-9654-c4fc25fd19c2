/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/index/index" |
       "/pages/about/about" |
       "/pages/annotation/annotationDetail" |
       "/pages/annotation/annotationList" |
       "/pages/demo/demo" |
       "/pages/demo/form" |
       "/pages/demo/indexBar" |
       "/pages/demo/selectPicker" |
       "/pages/demo/sqliteDemo" |
       "/pages/demo/tree" |
       "/pages/inventory/inventory" |
       "/pages/login/login" |
       "/pages/message/message" |
       "/pages/more/more" |
       "/pages/user/people" |
       "/pages/useRecord/useRecord" |
       "/pages/workHome/index" |
       "/pages/inventory/startInventory/startInventory" |
       "/pages/useRecord/detail/detail" |
       "/pages-home/home/<USER>" |
       "/pages-message/chat/chat" |
       "/pages-message/contacts/contacts" |
       "/pages-message/personPage/personPage" |
       "/pages-message/tenant/tenant" |
       "/pages-user/location/location" |
       "/pages-user/userEdit/userEdit" |
       "/pages-work/dragPage/index" |
       "/pages-work/onlinePage/onlineAdd" |
       "/pages-work/onlinePage/onlineDetail" |
       "/pages-work/onlinePage/onlineEdit" |
       "/pages-sub/online/online" |
       "/pages-sub/online/onlineCard" |
       "/pages-sub/online/onlineTable";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/user/people"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
