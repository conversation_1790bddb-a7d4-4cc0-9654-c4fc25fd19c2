/**
 * 资产数据相关类型定义
 */

// 资产数据接口定义
export interface AssetData {
  id: number
  number: string
  name: string
  model: string
  categoryCode: string
  imageUrl: string
  status: string
  storageLocation: string
  entryDate: string | null
  additionMethod: string
  originalValue: number | null
  serviceLife: number | null
  expiryYear: string
  department: string
  factoryArea: string
  userId: string
  createTime: string
  version: string
}

// 查询范围接口
export interface QueryRange {
  startNumber?: string
  endNumber?: string
  limit?: number
  offset?: number
}
