<template>
	<scroll-view class="tab-scroll-sticky" 
		id="tabs" 
		:scroll-y="true"
		@scroll="tabScoll"
		:scroll-top="scrollInto" >
		<view class="scroll-content">
			<view class="scroll-tab">
				<view class="scroll-tab-list" 
					v-for="(item,index) in tabList" 
					:style="{color:activeTab == index?activeColor:'#333333'}"
					@click="changeTab(index)"
					:id="'tabs'+index"
					:key="index">
					<text class="scroll-tab-list-text">{{item.text}}</text>
				</view>
				<view class="scroll-tab-bar" :style="[tabBarStyle]"></view>
			</view>
		</view>
		<view class="scroll-warp">
			<view class="scroll-warp-list" :id="'wrap'+index" v-for="(item,index) in tabList" :key="index" v-show="activeTab === index">
				<slot :name="`tab-${index}`" :item="item" :index="index">
					<!-- 默认内容 -->
					<view class="empty-content">
						<text class="empty-text">暂无内容，请添加具体内容</text>
					</view>
				</slot>
			</view>
		</view>
	</scroll-view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'

// 定义接口类型
interface TabItem {
	text: string
	navTarget?: string
	[key: string]: any
}

// 定义Props
interface Props {
	tabList: TabItem[]
	fixedTop?: number
	activeColor?: string
}

// 定义Props默认值
const props = withDefaults(defineProps<Props>(), {
	tabList: () => [],
	fixedTop: 44,
	activeColor: 'red'
})

// 定义Emits
const emit = defineEmits<{
	change: [index: number, item: TabItem]
}>()

// 响应式数据
const activeTab = ref(0)
const scrollInto = ref(0)
const tabBarStyle = ref({})

// 获取节点信息
const getRect = (selector: string): Promise<any> => {
	return new Promise((resolve) => {
		const view = uni.createSelectorQuery()
		view.select(selector).boundingClientRect((rect: any) => {
			resolve(rect || null)
		}).exec()
	})
}

// tab切换
const changeTab = async (index: number) => {
	activeTab.value = index
	await getTabRect(activeTab.value)
	
	// 由于使用v-show，不需要滚动到特定位置
	// 只需要重置滚动位置到顶部
	scrollInto.value = 0
	
	// 触发事件
	emit('change', index, props.tabList[index])
}

// 获取tab宽度
const getTabRect = async (itemIndex: number) => {
	const rect = await getRect("#tabs" + itemIndex)
	const rect1 = await getRect("#tabs" + itemIndex + ">.scroll-tab-list-text")
	
	if (!rect || !rect1) return
	
	const width = (rect1.width * 0.67)
	
	tabBarStyle.value = {
		left: (rect.left + (rect.width - width) / 2) + 'px',
		width: width + 'px',
		background: props.activeColor
	}
}

// scroll滚动
const tabScoll = async (e: any) => {
	// 由于移除了吸顶功能，这里可以简化或移除
	// 如果需要其他滚动相关功能，可以在这里添加
}

// 初始化tab指示器
const barInit = async () => {
	// 初始化当前激活tab的指示器位置
	await getTabRect(activeTab.value)
}

// 监听tabList变化，重新初始化
watch(() => props.tabList, () => {
	nextTick(() => {
		barInit()
		getTabRect(0)
	})
}, { deep: true })

// 组件挂载后初始化
onMounted(() => {
	nextTick(async () => {
		await barInit()
		await getTabRect(0)
	})
})
</script>

<style lang="scss" scoped>
	.flexRowCc{
		display: flex;
		flex-direction: row;
		align-items: center;
	}
	.scroll-content{
		position: relative;
		.scroll-tab{
			@extend .flexRowCc;
			justify-content: space-between;
			width: 100%;
			height: 44px;
			box-sizing: border-box;
			border-top: 1px solid #F1F1F1;
			border-bottom: 1px solid #F1F1F1;
			background: #FFFFFF;
			position: relative;
			&-list{
				text-align: center;
				font-size: 32rpx;
				flex:1 1 auto;
				&-text{
					display: inline-block;
				}
			}
			&-bar{
				width: 64rpx;
				height: 4rpx;
				background: rgba(222, 37, 37, 100);
				position: absolute;
				bottom: 8rpx;
				border-radius: 16rpx;
				transition-duration:.5s;
			}
		}
	}
	.scroll-warp{
		height: 100vh;
	}

	.empty-content {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 400rpx;
		padding: 40rpx;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999;
		text-align: center;
	}

</style>
