<template>
	<view class="example-container">
		<Tabs 
			:tabList="tabList" 
			:activeColor="'#007aff'"
			@change="handleTabChange"
		>
			<!-- 第一个tab的内容 -->
			<template #tab-0>
				<view class="tab-content">
					<view v-for="i in 20" :key="i" class="content-item">
						第一个Tab的内容 - {{ i }}
					</view>
				</view>
			</template>
			
			<!-- 第二个tab的内容 -->
			<template #tab-1>
				<view class="tab-content">
					<view v-for="i in 15" :key="i" class="content-item">
						第二个Tab的内容 - {{ i }}
					</view>
				</view>
			</template>
			
			<!-- 第三个tab的内容 -->
			<template #tab-2>
				<view class="tab-content">
					<view v-for="i in 25" :key="i" class="content-item">
						第三个Tab的内容 - {{ i }}
					</view>
				</view>
			</template>
			
			<!-- 第四个tab的内容 -->
			<template #tab-3>
				<view class="tab-content">
					<view v-for="i in 10" :key="i" class="content-item">
						第四个Tab的内容 - {{ i }}
					</view>
				</view>
			</template>
		</Tabs>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Tabs from './index.vue'

// Tab数据
const tabList = ref([
	{
		text: "全部",
		navTarget: "全部内容",
		value: "all"
	},
	{
		text: "待处理",
		navTarget: "待处理内容",
		value: "pending"
	},
	{
		text: "已完成",
		navTarget: "已完成内容",
		value: "completed"
	},
	{
		text: "已取消",
		navTarget: "已取消内容",
		value: "cancelled"
	}
])

// 处理tab切换
const handleTabChange = (index: number, item: any) => {
	console.log('切换到tab:', index, item)
	// 这里可以处理tab切换的逻辑
	// 比如加载不同的数据、更新状态等
}
</script>

<style scoped>
.example-container {
	height: 100vh;
}

.tab-content {
	padding: 20rpx;
}

.content-item {
	padding: 20rpx;
	margin-bottom: 10rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
}
</style>
