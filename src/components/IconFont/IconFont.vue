<template>
  <text 
    :class="['u-iconfont', `u-icon-${iconName}`, customClass]"
    :style="iconStyle"
  ></text>
</template>

<script setup lang="ts">
import { computed } from 'vue'

defineOptions({
  name: 'IconFont',
  options: {
    styleIsolation: 'shared',
  },
})

interface Props {
  iconName: string
  size?: string | number
  color?: string
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: '16px',
  color: '#333',
  customClass: ''
})

const iconStyle = computed(() => ({
  fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
  color: props.color
}))
</script>

<style lang="scss" scoped>
@import '@/static/iconfont/iconfont.css';
</style>
