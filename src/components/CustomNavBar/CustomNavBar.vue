<template>
	<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
		<view class="navbar-content">
			<!-- 左侧按钮区域 -->
			<view class="navbar-left">
				<!-- 返回上一页按钮 -->
				<view 
					v-if="showBack" 
					class="nav-btn" 
					@click="handleBack"
				>
					<image 
						class="nav-icon" 
						:src="backIcon" 
						mode="aspectFit"
					></image>
				</view>
				
				<!-- 返回主页按钮 -->
				<view 
					v-if="showHome" 
					class="nav-btn" 
					@click="handleHome"
				>
					<image 
						class="nav-icon" 
						:src="homeIcon" 
						mode="aspectFit"
					></image>
				</view>
			</view>
			
			<!-- 中间标题区域 -->
			<view class="navbar-center">
				<text class="navbar-title" :style="{ color: titleColor }">{{ title }}</text>
			</view>
			
			<!-- 右侧按钮区域 -->
			<view class="navbar-right">
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 定义组件属性
const props = defineProps({
	// 标题
	title: {
		type: String,
		default: ''
	},
	// 是否显示返回上一页按钮
	showBack: {
		type: Boolean,
		default: true
	},
	// 是否显示返回主页按钮
	showHome: {
		type: Boolean,
		default: false
	},
	// 标题颜色
	titleColor: {
		type: String,
		default: '#000000'
	},
	// 按钮图标颜色主题 (black/white)
	iconTheme: {
		type: String,
		default: 'black'
	},
	// 背景颜色
	backgroundColor: {
		type: String,
		default: '#FFFFFF'
	}
})

// 定义事件
const emit = defineEmits(['back', 'home'])

// 获取系统信息
const systemInfo = uni.getSystemInfoSync()
const statusBarHeight = systemInfo.statusBarHeight || 0

// 计算图标路径
const backIcon = computed(() => {
	return props.iconTheme === 'white' 
		? '/static/zhouWei-navBar/icon_back_white.png'
		: '/static/zhouWei-navBar/icon_back_black.png'
})

const homeIcon = computed(() => {
	return props.iconTheme === 'white'
		? '/static/zhouWei-navBar/icon_home_white.png'
		: '/static/zhouWei-navBar/icon_home_black.png'
})

// 处理返回上一页
const handleBack = () => {
	emit('back')
	// 默认行为：返回上一页
	uni.navigateBack()
}

// 处理返回主页
const handleHome = () => {
	emit('home')
	// 默认行为：跳转到首页，使用 reLaunch 确保清空页面栈
	uni.reLaunch({
		url: '/pages/index/index'
	})
}
</script>

<style scoped>
.custom-navbar {
	background-color: v-bind(backgroundColor);
	position: sticky;
	top: 0;
	z-index: 999;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 30rpx;
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.navbar-center {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}

.navbar-right {
	display: flex;
	align-items: center;
	min-width: 80rpx;
}

.nav-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.nav-btn:active {
	background-color: rgba(0, 0, 0, 0.1);
	transform: scale(0.95);
}

.nav-icon {
	width: 36rpx;
	height: 36rpx;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: 600;
	text-align: center;
}
</style>
