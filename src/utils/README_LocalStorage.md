# LocalStorage 资产数据存储使用说明

## 概述

本项目提供了完整的LocalStorage数据存储解决方案，用于存储和管理资产数据。支持按资产编号范围查询、精确查询、批量插入等功能。

## 文件结构

- `src/utils/dataManager.ts` - 数据管理器
- `src/utils/localStorageManager.ts` - LocalStorage管理类
- `src/utils/assetDataGenerator.ts` - 示例数据生成器
- `src/pages/demo/localStorageDemo.vue` - 演示页面

## 主要功能

### 1. 数据管理器初始化
```typescript
import { dataManager } from '@/utils/dataManager'

// 初始化存储
await dataManager.initDatabase()
```

### 2. 插入数据
```typescript
// 插入单条数据
const asset: AssetData = {
  id: 2623,
  number: "ASSET-20250908-001",
  name: "笔记本电脑4",
  model: "联想ThinkPad",
  categoryCode: "1",
  imageUrl: "",
  status: "0",
  storageLocation: "1",
  entryDate: "2025-01-08",
  additionMethod: "购入",
  originalValue: 5000,
  serviceLife: 5,
  expiryYear: "2030",
  department: "技术部",
  factoryArea: "新港",
  userId: "admin",
  createTime: "2025-01-08T10:30:00.000Z",
  version: "1.0.0"
}

await dataManager.insertAsset(asset)

// 批量插入数据
const assets: AssetData[] = [asset1, asset2, asset3]
await dataManager.insertAssets(assets)
```

### 3. 查询数据
```typescript
// 范围查询
const results = await dataManager.queryAssetsByNumberRange({
  startNumber: "ASSET-20250101-001",
  endNumber: "ASSET-20250101-100",
  limit: 50
})

// 精确查询
const asset = await dataManager.queryAssetByNumber("ASSET-20250101-001")

// 获取所有数据
const allAssets = await dataManager.getAllAssets()

// 获取总数
const count = await dataManager.getAssetCount()
```

### 4. 数据管理
```typescript
// 清空所有数据
await dataManager.clearAllAssets()

// 导出数据
const backupData = dataManager.exportData()

// 导入数据
await dataManager.importData(backupData)

// 获取存储信息
const info = dataManager.getStorageInfo()
console.log(info)
// 输出：
// {
//   storageType: 'localStorage',
//   totalCount: 1000,
//   estimatedSize: '2.5 MB',
//   lastUpdate: '2025-01-08T10:30:00.000Z'
// }
```

## 使用示例

### 生成并插入1000条示例数据
```typescript
import { initAssetDatabase } from '@/utils/assetDataGenerator'

// 初始化存储并插入示例数据
await initAssetDatabase()
```

### 测试查询功能
```typescript
import { testQueryFunctions } from '@/utils/assetDataGenerator'

// 测试各种查询功能
await testQueryFunctions()
```

## 演示页面

访问 `/pages/demo/localStorageDemo` 页面可以查看完整的演示功能，包括：
- 存储初始化
- 范围查询
- 精确查询
- 统计信息
- 实时日志

## 注意事项

1. **存储限制**：LocalStorage通常有5-10MB的存储限制
2. **数据持久化**：数据在应用重启后依然保持
3. **性能优秀**：适合中等规模数据（建议<5MB）
4. **真机兼容**：在所有真机环境中100%可用
5. **无需配置**：不需要manifest.json配置

## API 参考

### DataManager 类

#### 方法

- `initDatabase()` - 初始化存储
- `insertAsset(asset)` - 插入单条资产
- `insertAssets(assets)` - 批量插入资产
- `queryAssetsByNumberRange(range)` - 范围查询
- `queryAssetByNumber(number)` - 精确查询
- `getAllAssets()` - 获取所有资产
- `getAssetCount()` - 获取资产总数
- `clearAllAssets()` - 清空所有资产
- `getStorageInfo()` - 获取存储信息
- `exportData()` - 导出数据
- `importData(jsonData)` - 导入数据

### AssetData 接口

```typescript
interface AssetData {
  id: number
  number: string
  name: string
  model: string
  categoryCode: string
  imageUrl: string
  status: string
  storageLocation: string
  entryDate: string | null
  additionMethod: string
  originalValue: number | null
  serviceLife: number | null
  expiryYear: string
  department: string
  factoryArea: string
  userId: string
  createTime: string
  version: string
}
```

### QueryRange 接口

```typescript
interface QueryRange {
  startNumber?: string
  endNumber?: string
  limit?: number
  offset?: number
}
```

## 性能建议

1. **批量操作**：使用 `insertAssets()` 而不是多次调用 `insertAsset()`
2. **分页查询**：对于大量数据，使用 `limit` 和 `offset` 参数
3. **定期清理**：定期清理不需要的数据以节省存储空间
4. **数据备份**：定期使用 `exportData()` 备份重要数据

## 错误处理

```typescript
try {
  await dataManager.initDatabase()
  await dataManager.insertAssets(assets)
} catch (error) {
  console.error('操作失败:', error)
  // 处理错误
}
```

## 迁移指南

如果您之前使用SQLite，迁移到LocalStorage非常简单：

1. 更新导入语句：
```typescript
// 旧代码
import { sqliteManager } from '@/utils/sqlite'

// 新代码
import { dataManager } from '@/utils/dataManager'
```

2. 更新API调用：
```typescript
// 旧代码
await sqliteManager.initDatabase()

// 新代码
await dataManager.initDatabase()
```

所有其他API保持不变！
