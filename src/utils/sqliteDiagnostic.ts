/**
 * SQLite 诊断工具
 * 用于排查SQLite模块不可用的问题
 */

export interface DiagnosticResult {
  step: string
  success: boolean
  message: string
  details?: any
}

export class SQLiteDiagnostic {
  private results: DiagnosticResult[] = []

  /**
   * 运行完整的SQLite诊断
   */
  async runFullDiagnostic(): Promise<DiagnosticResult[]> {
    this.results = []
    
    // 步骤1: 检查运行环境
    this.checkEnvironment()
    
    // 步骤2: 检查plus对象
    this.checkPlusObject()
    
    // 步骤3: 检查SQLite模块
    this.checkSQLiteModule()
    
    // 步骤4: 测试数据库操作
    await this.testDatabaseOperations()
    
    return this.results
  }

  /**
   * 检查运行环境
   */
  private checkEnvironment() {
    try {
      // #ifdef APP-PLUS
      this.addResult('环境检查', true, 'APP-PLUS环境', {
        userAgent: navigator.userAgent,
        platform: navigator.platform
      })
      // #endif
      
      // #ifdef H5
      this.addResult('环境检查', true, 'H5环境', {
        userAgent: navigator.userAgent,
        platform: navigator.platform
      })
      // #endif
      
      // #ifdef MP-WEIXIN
      this.addResult('环境检查', true, '微信小程序环境')
      // #endif
      
    } catch (error) {
      this.addResult('环境检查', false, `环境检查失败: ${error}`)
    }
  }

  /**
   * 检查plus对象
   */
  private checkPlusObject() {
    try {
      // #ifdef APP-PLUS
      if (typeof plus === 'undefined') {
        this.addResult('Plus对象检查', false, 'plus对象未定义，可能不在App环境中运行')
        return
      }

      this.addResult('Plus对象检查', true, 'plus对象可用', {
        plusVersion: plus.runtime.version,
        plusBuild: plus.runtime.build,
        availableModules: Object.keys(plus).filter(key => typeof plus[key] === 'object')
      })
      // #endif
      
      // #ifdef H5
      this.addResult('Plus对象检查', true, 'H5环境，跳过plus检查')
      // #endif
      
    } catch (error) {
      this.addResult('Plus对象检查', false, `Plus对象检查失败: ${error}`)
    }
  }

  /**
   * 检查SQLite模块
   */
  private checkSQLiteModule() {
    try {
      // #ifdef APP-PLUS
      if (typeof plus === 'undefined') {
        this.addResult('SQLite模块检查', false, 'plus对象不可用')
        return
      }

      if (!plus.sqlite) {
        this.addResult('SQLite模块检查', false, 'SQLite模块未加载', {
          availableModules: Object.keys(plus).filter(key => typeof plus[key] === 'object'),
          manifestCheck: '请检查manifest.json中是否配置了SQLite模块'
        })
        return
      }

      this.addResult('SQLite模块检查', true, 'SQLite模块可用', {
        sqliteMethods: Object.keys(plus.sqlite),
        openDatabaseType: typeof plus.sqlite.openDatabase,
        closeDatabaseType: typeof plus.sqlite.closeDatabase
      })
      // #endif
      
      // #ifdef H5
      this.addResult('SQLite模块检查', true, 'H5环境，使用模拟SQLite')
      // #endif
      
    } catch (error) {
      this.addResult('SQLite模块检查', false, `SQLite模块检查失败: ${error}`)
    }
  }

  /**
   * 测试数据库操作
   */
  private async testDatabaseOperations(): Promise<void> {
    try {
      // #ifdef APP-PLUS
      if (typeof plus === 'undefined' || !plus.sqlite) {
        this.addResult('数据库操作测试', false, 'SQLite模块不可用，跳过测试')
        return
      }

      // 测试打开数据库
      let testDb: any = null
      try {
        testDb = plus.sqlite.openDatabase({
          name: 'diagnostic_test.db'
        })
        
        if (testDb) {
          this.addResult('数据库打开测试', true, '数据库打开成功', {
            databaseObject: typeof testDb,
            databaseMethods: Object.keys(testDb)
          })
          
          // 测试执行SQL
          await this.testSQLExecution(testDb)
          
        } else {
          this.addResult('数据库打开测试', false, '数据库对象为null')
        }
      } catch (dbError) {
        this.addResult('数据库打开测试', false, `数据库打开失败: ${dbError}`)
      } finally {
        // 清理测试数据库
        if (testDb) {
          try {
            plus.sqlite.closeDatabase(testDb)
          } catch (closeError) {
            console.warn('关闭测试数据库失败:', closeError)
          }
        }
      }
      // #endif
      
      // #ifdef H5
      this.addResult('数据库操作测试', true, 'H5环境，使用模拟数据库操作')
      // #endif
      
    } catch (error) {
      this.addResult('数据库操作测试', false, `数据库操作测试失败: ${error}`)
    }
  }

  /**
   * 测试SQL执行
   */
  private async testSQLExecution(db: any): Promise<void> {
    return new Promise((resolve) => {
      try {
        db.transaction((tx: any) => {
          tx.executeSql(
            'CREATE TABLE IF NOT EXISTS diagnostic_test (id INTEGER PRIMARY KEY, name TEXT)',
            [],
            () => {
              this.addResult('SQL执行测试', true, 'SQL执行成功')
              resolve()
            },
            (error: any) => {
              this.addResult('SQL执行测试', false, `SQL执行失败: ${error}`)
              resolve()
            }
          )
        }, (error: any) => {
          this.addResult('SQL执行测试', false, `事务执行失败: ${error}`)
          resolve()
        })
      } catch (error) {
        this.addResult('SQL执行测试', false, `SQL执行测试异常: ${error}`)
        resolve()
      }
    })
  }

  /**
   * 添加诊断结果
   */
  private addResult(step: string, success: boolean, message: string, details?: any) {
    this.results.push({
      step,
      success,
      message,
      details
    })
    
    // 同时输出到控制台
    const logMethod = success ? console.log : console.error
    logMethod(`[SQLite诊断] ${step}: ${message}`, details || '')
  }

  /**
   * 获取诊断报告
   */
  getReport(): string {
    let report = '=== SQLite 诊断报告 ===\n\n'
    
    this.results.forEach((result, index) => {
      report += `${index + 1}. ${result.step}: ${result.success ? '✅' : '❌'}\n`
      report += `   ${result.message}\n`
      if (result.details) {
        report += `   详细信息: ${JSON.stringify(result.details, null, 2)}\n`
      }
      report += '\n'
    })
    
    // 添加建议
    const failedSteps = this.results.filter(r => !r.success)
    if (failedSteps.length > 0) {
      report += '=== 问题解决建议 ===\n\n'
      
      failedSteps.forEach(step => {
        switch (step.step) {
          case 'Plus对象检查':
            report += '• Plus对象不可用: 确保在真机App环境中运行，不是H5或模拟器\n'
            break
          case 'SQLite模块检查':
            report += '• SQLite模块不可用: \n'
            report += '  1. 检查manifest.json中是否配置了SQLite模块\n'
            report += '  2. 重新打包App（云端打包或本地打包）\n'
            report += '  3. 确保使用的是正式版HBuilderX\n'
            break
          case '数据库打开测试':
            report += '• 数据库打开失败: 检查设备存储权限和SQLite模块版本\n'
            break
          case 'SQL执行测试':
            report += '• SQL执行失败: 检查数据库文件权限和SQL语法\n'
            break
        }
      })
    } else {
      report += '=== 诊断结果 ===\n\n'
      report += '✅ 所有检查项目都通过了！SQLite模块工作正常。\n'
    }
    
    return report
  }
}

// 创建单例实例
export const sqliteDiagnostic = new SQLiteDiagnostic()
