/**
 * 数据管理器
 * 基于LocalStorage的资产数据管理
 */

import { localStorageManager } from './localStorageManager'
import { AssetData, QueryRange } from '../types/asset'

// 数据管理器接口
export interface IDataManager {
  initDatabase(): Promise<void>
  insertAsset(asset: AssetData): Promise<void>
  insertAssets(assets: AssetData[]): Promise<void>
  queryAssetsByNumberRange(queryRange: QueryRange): Promise<AssetData[]>
  queryAssetByNumber(number: string): Promise<AssetData | null>
  getAllAssets(): Promise<AssetData[]>
  getAssetCount(): Promise<number>
  clearAllAssets(): Promise<void>
}

class DataManager implements IDataManager {
  private initPromise: Promise<void> | null = null

  /**
   * 初始化LocalStorage存储
   */
  async initDatabase(): Promise<void> {
    // 如果已经初始化过，直接返回
    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._initDatabase()
    return this.initPromise
  }

  private async _initDatabase(): Promise<void> {
    console.log('=== 数据管理器初始化 ===')

    try {
      console.log('初始化LocalStorage...')
      await localStorageManager.initStorage()
      console.log('✅ LocalStorage初始化成功')
    } catch (error) {
      console.error('❌ LocalStorage初始化失败:', error)
      throw new Error('LocalStorage初始化失败')
    }
  }

  /**
   * 获取存储类型
   */
  getStorageType(): string {
    return 'localStorage'
  }

  // 实现IDataManager接口的所有方法

  async insertAsset(asset: AssetData): Promise<void> {
    return localStorageManager.insertAsset(asset)
  }

  async insertAssets(assets: AssetData[]): Promise<void> {
    return localStorageManager.insertAssets(assets)
  }

  async queryAssetsByNumberRange(queryRange: QueryRange): Promise<AssetData[]> {
    return localStorageManager.queryAssetsByNumberRange(queryRange)
  }

  async queryAssetByNumber(number: string): Promise<AssetData | null> {
    return localStorageManager.queryAssetByNumber(number)
  }

  async getAllAssets(): Promise<AssetData[]> {
    return localStorageManager.getAllAssets()
  }

  async getAssetCount(): Promise<number> {
    return localStorageManager.getAssetCount()
  }

  async clearAllAssets(): Promise<void> {
    return localStorageManager.clearAllAssets()
  }

  /**
   * 获取存储信息
   */
  getStorageInfo(): any {
    return {
      storageType: 'localStorage',
      initialized: true,
      ...localStorageManager.getStorageInfo()
    }
  }

  /**
   * 导出数据（用于备份）
   */
  exportData(): string {
    return localStorageManager.exportData()
  }

  /**
   * 导入数据（用于恢复）
   */
  async importData(jsonData: string): Promise<void> {
    return localStorageManager.importData(jsonData)
  }
}

// 创建单例实例
export const dataManager = new DataManager()

// 导出相关类型
export { AssetData, QueryRange }
