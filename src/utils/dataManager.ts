/**
 * 统一数据管理器
 * 自动选择最佳的存储方案（SQLite或LocalStorage）
 */

import { AssetData, QueryRange, sqliteManager } from './sqlite.ts'
import { localStorageManager } from './localStorageManager'

// 存储类型枚举
export enum StorageType {
  SQLITE = 'sqlite',
  LOCALSTORAGE = 'localStorage'
}

// 数据管理器接口
export interface IDataManager {
  initDatabase(): Promise<void>
  insertAsset(asset: AssetData): Promise<void>
  insertAssets(assets: AssetData[]): Promise<void>
  queryAssetsByNumberRange(queryRange: QueryRange): Promise<AssetData[]>
  queryAssetByNumber(number: string): Promise<AssetData | null>
  getAllAssets(): Promise<AssetData[]>
  getAssetCount(): Promise<number>
  clearAllAssets(): Promise<void>
}

class UnifiedDataManager implements IDataManager {
  private currentManager: IDataManager | null = null
  private storageType: StorageType | null = null
  private initPromise: Promise<void> | null = null

  /**
   * 自动初始化最佳存储方案
   */
  async initDatabase(): Promise<void> {
    // 如果已经初始化过，直接返回
    if (this.initPromise) {
      return this.initPromise
    }

    this.initPromise = this._initDatabase()
    return this.initPromise
  }

  private async _initDatabase(): Promise<void> {
    console.log('=== 统一数据管理器初始化 ===')
    
    // 优先尝试SQLite
    try {
      console.log('1. 尝试初始化SQLite...')
      await sqliteManager.initDatabase()
      this.currentManager = sqliteManager
      this.storageType = StorageType.SQLITE
      console.log('✅ SQLite初始化成功')
      return
    } catch (sqliteError) {
      console.warn('⚠️ SQLite初始化失败:', sqliteError)
    }

    // SQLite失败，使用LocalStorage
    try {
      console.log('2. 尝试初始化LocalStorage...')
      await localStorageManager.initStorage()
      this.currentManager = localStorageManager
      this.storageType = StorageType.LOCALSTORAGE
      console.log('✅ LocalStorage初始化成功')
      return
    } catch (localStorageError) {
      console.error('❌ LocalStorage初始化失败:', localStorageError)
      throw new Error('所有存储方案都初始化失败')
    }
  }

  /**
   * 获取当前使用的存储类型
   */
  getStorageType(): StorageType | null {
    return this.storageType
  }

  /**
   * 获取当前管理器实例
   */
  getCurrentManager(): IDataManager | null {
    return this.currentManager
  }

  /**
   * 检查是否已初始化
   */
  private ensureInitialized(): void {
    if (!this.currentManager) {
      throw new Error('数据管理器未初始化，请先调用 initDatabase()')
    }
  }

  // 实现IDataManager接口的所有方法

  async insertAsset(asset: AssetData): Promise<void> {
    this.ensureInitialized()
    return this.currentManager!.insertAsset(asset)
  }

  async insertAssets(assets: AssetData[]): Promise<void> {
    this.ensureInitialized()
    return this.currentManager!.insertAssets(assets)
  }

  async queryAssetsByNumberRange(queryRange: QueryRange): Promise<AssetData[]> {
    this.ensureInitialized()
    return this.currentManager!.queryAssetsByNumberRange(queryRange)
  }

  async queryAssetByNumber(number: string): Promise<AssetData | null> {
    this.ensureInitialized()
    return this.currentManager!.queryAssetByNumber(number)
  }

  async getAllAssets(): Promise<AssetData[]> {
    this.ensureInitialized()
    return this.currentManager!.getAllAssets()
  }

  async getAssetCount(): Promise<number> {
    this.ensureInitialized()
    return this.currentManager!.getAssetCount()
  }

  async clearAllAssets(): Promise<void> {
    this.ensureInitialized()
    return this.currentManager!.clearAllAssets()
  }

  /**
   * 获取存储信息
   */
  getStorageInfo(): any {
    if (!this.currentManager) {
      return {
        storageType: null,
        initialized: false,
        message: '未初始化'
      }
    }

    const baseInfo = {
      storageType: this.storageType,
      initialized: true
    }

    // 如果是LocalStorage，获取详细信息
    if (this.storageType === StorageType.LOCALSTORAGE) {
      return {
        ...baseInfo,
        ...(localStorageManager as any).getStorageInfo()
      }
    }

    // SQLite的基本信息
    return baseInfo
  }

  /**
   * 数据迁移（从SQLite到LocalStorage或反之）
   */
  async migrateData(targetType: StorageType): Promise<void> {
    if (!this.currentManager || this.storageType === targetType) {
      console.log('无需迁移数据')
      return
    }

    console.log(`开始数据迁移: ${this.storageType} -> ${targetType}`)

    try {
      // 获取当前所有数据
      const allAssets = await this.getAllAssets()
      console.log(`准备迁移 ${allAssets.length} 条数据`)

      // 初始化目标存储
      let targetManager: IDataManager
      if (targetType === StorageType.SQLITE) {
        await sqliteManager.initDatabase()
        targetManager = sqliteManager
      } else {
        await localStorageManager.initStorage()
        targetManager = localStorageManager
      }

      // 清空目标存储
      await targetManager.clearAllAssets()

      // 批量插入数据
      if (allAssets.length > 0) {
        await targetManager.insertAssets(allAssets)
      }

      // 切换到新的管理器
      this.currentManager = targetManager
      this.storageType = targetType

      console.log(`数据迁移完成: ${allAssets.length} 条数据`)

    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }

  /**
   * 强制使用指定的存储类型
   */
  async forceUseStorage(storageType: StorageType): Promise<void> {
    console.log(`强制使用存储类型: ${storageType}`)

    try {
      if (storageType === StorageType.SQLITE) {
        await sqliteManager.initDatabase()
        this.currentManager = sqliteManager
        this.storageType = StorageType.SQLITE
      } else {
        await localStorageManager.initStorage()
        this.currentManager = localStorageManager
        this.storageType = StorageType.LOCALSTORAGE
      }

      console.log(`强制使用 ${storageType} 成功`)

    } catch (error) {
      console.error(`强制使用 ${storageType} 失败:`, error)
      throw error
    }
  }
}

// 创建单例实例
export const dataManager = new UnifiedDataManager()

// 导出相关类型
export { AssetData, QueryRange }
