/**
 * 资产数据生成器
 * 用于生成示例数据并插入到SQLite数据库
 */

import { sqliteManager, AssetData } from './sqlite'

/**
 * 生成示例资产数据
 */
export function generateSampleAssets(count: number = 1000): AssetData[] {
  const assets: AssetData[] = []
  const departments = ['新港', '技术部', '财务部', '人事部', '市场部', '运营部']
  const categories = ['0', '1', '2', '3', '4', '5']
  const statuses = ['0', '1', '2', '3']
  const additionMethods = ['购入', '捐赠', '租赁', '调拨']
  const models = ['惠普', '联想', '戴尔', '华硕', '苹果', '华为']
  const storageLocations = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']

  for (let i = 1; i <= count; i++) {
    const date = new Date()
    date.setDate(date.getDate() - Math.floor(Math.random() * 365))
    
    const asset: AssetData = {
      id: 2000 + i, // 从2000开始，避免与现有数据冲突
      number: `ASSET-${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}-${String(i).padStart(3, '0')}`,
      name: `资产${i}`,
      model: models[Math.floor(Math.random() * models.length)],
      categoryCode: categories[Math.floor(Math.random() * categories.length)],
      imageUrl: `https://obsqingnang.guoyaoplat.com/book/temp/asset_${i}.png`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      storageLocation: storageLocations[Math.floor(Math.random() * storageLocations.length)],
      entryDate: Math.random() > 0.5 ? date.toISOString().split('T')[0] : null,
      additionMethod: additionMethods[Math.floor(Math.random() * additionMethods.length)],
      originalValue: Math.random() > 0.3 ? Math.floor(Math.random() * 50000) + 1000 : null,
      serviceLife: Math.random() > 0.4 ? Math.floor(Math.random() * 10) + 1 : null,
      expiryYear: String(2025 + Math.floor(Math.random() * 10)),
      department: departments[Math.floor(Math.random() * departments.length)],
      factoryArea: Math.random() > 0.5 ? `区域${Math.floor(Math.random() * 5) + 1}` : '',
      userId: `user_${Math.random().toString(36).substr(2, 9)}`,
      createTime: date.toISOString().replace('T', ' ').substr(0, 19),
      version: '1.0.0'
    }
    
    assets.push(asset)
  }

  return assets
}

/**
 * 初始化数据库并插入示例数据
 */
export async function initAssetDatabase(): Promise<void> {
  try {
    console.log('正在初始化数据库...')
    await sqliteManager.initDatabase()
    
    console.log('正在清空现有数据...')
    await sqliteManager.clearAllAssets()
    
    console.log('正在生成示例数据...')
    const sampleAssets = generateSampleAssets(1000)
    
    console.log('正在插入数据到数据库...')
    await sqliteManager.insertAssets(sampleAssets)
    
    const count = await sqliteManager.getAssetCount()
    console.log(`数据库初始化完成，共插入 ${count} 条资产数据`)
    
  } catch (error) {
    console.error('数据库初始化失败:', error)
    throw error
  }
}

/**
 * 测试查询功能
 */
export async function testQueryFunctions(): Promise<void> {
  try {
    console.log('开始测试查询功能...')
    
    // 测试获取总数
    const totalCount = await sqliteManager.getAssetCount()
    console.log(`总资产数量: ${totalCount}`)
    
    // 测试范围查询
    const rangeQuery = await sqliteManager.queryAssetsByNumberRange({
      startNumber: 'ASSET-20250101-001',
      endNumber: 'ASSET-20250101-010',
      limit: 10
    })
    console.log(`范围查询结果 (ASSET-20250101-001 到 ASSET-20250101-010):`, rangeQuery.length, '条')
    
    // 测试精确查询
    const exactQuery = await sqliteManager.queryAssetByNumber('ASSET-20250101-001')
    console.log('精确查询结果:', exactQuery ? '找到' : '未找到')
    
    // 测试分页查询
    const pageQuery = await sqliteManager.queryAssetsByNumberRange({
      limit: 5,
      offset: 0
    })
    console.log(`分页查询结果 (前5条):`, pageQuery.length, '条')
    
    console.log('查询功能测试完成')
    
  } catch (error) {
    console.error('查询功能测试失败:', error)
    throw error
  }
}
