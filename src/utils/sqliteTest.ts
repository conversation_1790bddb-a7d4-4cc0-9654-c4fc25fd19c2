/**
 * SQLite 测试工具
 * 用于测试SQLite模块是否正常工作
 */

// 测试SQLite模块是否可用
export const testSQLiteModule = (): boolean => {
  try {
    // #ifdef APP-PLUS
    if (typeof plus === 'undefined') {
      console.log('plus环境不可用')
      return false
    }
    
    if (!plus.sqlite) {
      console.log('plus.sqlite不可用')
      return false
    }
    
    console.log('plus.sqlite可用:', !!plus.sqlite)
    console.log('plus.sqlite方法:', Object.keys(plus.sqlite))
    
    // 尝试打开一个测试数据库
    const testDb = (plus as any).sqlite.openDatabase({
      name: 'test.db'
    })
    
    console.log('测试数据库对象:', testDb)
    
    if (testDb) {
      console.log('SQLite模块测试成功')
      return true
    } else {
      console.log('SQLite模块测试失败 - 数据库对象为null')
      return false
    }
    // #endif
    
    // #ifdef H5
    console.log('H5环境，跳过SQLite测试')
    return true
    // #endif
    
  } catch (error) {
    console.error('SQLite模块测试异常:', error)
    return false
  }
}

// 测试数据库操作
export const testDatabaseOperations = async (): Promise<boolean> => {
  try {
    // #ifdef APP-PLUS
    if (typeof plus === 'undefined' || !plus.sqlite) {
      console.log('plus.sqlite不可用，无法测试数据库操作')
      return false
    }
    
    const db = (plus as any).sqlite.openDatabase({
      name: 'test_operations.db'
    })
    
    if (!db) {
      console.log('无法打开测试数据库')
      return false
    }
    
    console.log('测试数据库已打开')
    
    // 测试创建表
    return new Promise((resolve) => {
      (db as any).transaction((tx: any) => {
        tx.executeSql(
          'CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, name TEXT)',
          [],
          () => {
            console.log('测试表创建成功')
            resolve(true)
          },
          (error: any) => {
            console.error('测试表创建失败:', error)
            resolve(false)
          }
        )
      })
    })
    // #endif
    
    // #ifdef H5
    console.log('H5环境，跳过数据库操作测试')
    return true
    // #endif
    
  } catch (error) {
    console.error('数据库操作测试异常:', error)
    return false
  }
}
