// uhf-library.js

const uhfModule = uni.requireNativePlugin('UhfModule')
const scanModule = uni.requireNativePlugin('ScanModule');

class UHFManager {
  constructor() {
    this.isInitialized = false
    this.isInventoryRunning = false
    this.inventoryCallback = null
	this.result = null
  }
  initializeScan(option) {
	scanModule.init({ mode: 3 }, (res) => {
	  console.log('scan初始化成功')
	});
  }
  

  // 初始化UHF设备
  initialize(options = {}) {
    return new Promise((resolve, reject) => {
      const defaultOptions = {
        readPower: 30,
        writePower: 30,
        regional: 1,
      }

      const mergedOptions = { ...defaultOptions, ...options }

      uhfModule.initUHF(mergedOptions, (result) => {
        console.log('UHF initialize result:', result)

        if (result.success) {
          this.isInitialized = true
          resolve(result)
        } else {
          this.isInitialized = false
          reject(new Error(result.message || 'UHF initialization failed'))
        }
      })
    })
  }

  // 检查UHF状态
  checkStatus() {
    return new Promise((resolve, reject) => {
      uhfModule.checkUHFStatus((result) => {
        console.log('UHF status result:', result)

        this.isInitialized = result.initialized
        resolve(result)
      })
    })
  }

  // 开始盘点
  startInventory() {
    return new Promise((resolve, reject) => {
      uhfModule.startInventory((result) => {
        console.log('UHF start inventory result:', result)

        if (result.success) {
          this.isInventoryRunning = true
          resolve(result)
        } else {
          this.isInventoryRunning = false
          reject(new Error(result.message || 'Failed to start inventory'))
        }
      })
    })
  }

  // 停止盘点
  stopInventory() {
    return new Promise((resolve, reject) => {
      uhfModule.stopInventory((result) => {
        console.log('UHF stop inventory result:', result)

        if (result.success) {
          this.isInventoryRunning = false
          resolve(result)
        } else {
          reject(new Error(result.message || 'Failed to stop inventory'))
        }
      })
    })
  }

  // 设置扫描键状态
  setScanKeyEnabled(enabled) {
    return new Promise((resolve, reject) => {
      uhfModule.setScanKeyEnabled(enabled, (result) => {
        console.log('UHF set scan key result:', result)

        if (result.success) {
          resolve(result)
        } else {
          reject(new Error(result.message || 'Failed to set scan key'))
        }
      })
    })
  }

  // 设置盘点回调 - 持续接收标签数据
  onInventoryTag(callback) {
    this.inventoryCallback = callback
    console.log('设置盘点回调:', callback)

    // 注册全局事件监听器，接收原生模块发送的标签数据
    // uni.$on('uhf-tag-data', (data) => {
    //   console.log('收到 uhf-tag-data 事件:', data)
    //   if (this.inventoryCallback && typeof this.inventoryCallback === 'function') {
    //     console.log('调用回调函数，传入数据:', data.tags)
    //     this.inventoryCallback(data.tags)
    //   } else {
    //     console.log('回调函数未设置或无效')
    //   }
    // })

    console.log('事件监听器已注册')
  }

  // 移除盘点回调
  removeInventoryListener() {
    this.inventoryCallback = null
    uni.$off('uhf-tag-data')
  }
  
  // 开始扫码
    startScan() {
      scanModule.startScan({ timeout: 5, type: 1 }, (res) => {
        this.result = JSON.stringify(res);
        console.log('result', this.result)
      }, (err) => {
        uni.showModal({ content: '扫码失败：' + JSON.stringify(err) });
      });
	  }
	  stopScan() {
      scanModule.stopScan();
      this.result = '已停止';
	  }
}

// 创建全局单例
const uhfManager = new UHFManager()

// 导出实例和类
export default uhfManager
// export { UHFManager };
