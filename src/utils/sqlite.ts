/**
 * SQLite数据库工具类
 * 用于管理资产数据的本地存储
 */

// 资产数据接口定义
export interface AssetData {
  id: number
  number: string
  name: string
  model: string
  categoryCode: string
  imageUrl: string
  status: string
  storageLocation: string
  entryDate: string | null
  additionMethod: string
  originalValue: number | null
  serviceLife: number | null
  expiryYear: string
  department: string
  factoryArea: string
  userId: string
  createTime: string
  version: string
}

// 查询范围接口
export interface QueryRange {
  startNumber?: string
  endNumber?: string
  limit?: number
  offset?: number
}

class SQLiteManager {
  private db: any = null
  private dbName = 'assets.db'

  /**
   * 初始化数据库
   */
  async initDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // #ifdef APP-PLUS
        // 检查plus是否可用
        if (typeof plus === 'undefined') {
          reject(new Error('plus环境不可用'))
          return
        }
        
        // 检查SQLite模块是否可用
        if (!plus.sqlite) {
          reject(new Error('SQLite模块不可用，请确保在manifest.json中配置了SQLite模块'))
          return
        }
        
        console.log('plus.sqlite可用:', !!plus.sqlite)
        
        // 尝试打开数据库
        try {
          // 方法1: 不指定路径
          console.log('尝试方法1: 不指定路径打开数据库...')
          this.db = plus.sqlite.openDatabase({
            name: this.dbName
          })
          
          console.log('方法1结果 - 数据库对象:', this.db)
          
          // 如果方法1失败，尝试方法2
          if (!this.db) {
            console.log('方法1失败，尝试方法2: 指定路径打开数据库...')
            this.db = plus.sqlite.openDatabase({
              name: this.dbName,
              path: '_doc/assets.db'
            })
            console.log('方法2结果 - 数据库对象:', this.db)
          }
          
          // 如果方法2也失败，尝试方法3
          if (!this.db) {
            console.log('方法2失败，尝试方法3: 使用默认路径...')
            this.db = plus.sqlite.openDatabase({
              name: this.dbName,
              path: 'assets.db'
            })
            console.log('方法3结果 - 数据库对象:', this.db)
          }
          
          // 如果所有方法都失败
          if (!this.db) {
            console.error('所有数据库打开方法都失败了')
            reject(new Error('数据库打开失败，已尝试多种方式'))
            return
          }
          
          console.log('SQLite数据库已成功打开:', this.dbName)
        } catch (dbError) {
          console.error('打开数据库时发生异常:', dbError)
          reject(new Error(`数据库打开异常: ${dbError.message}`))
          return
        }
        // #endif

        // #ifdef H5
        // H5环境使用localStorage模拟
        this.db = {
          executeSql: (sql: string, params: any[] = []) => {
            return new Promise((resolve, reject) => {
              // 这里可以实现H5的模拟SQLite
              console.log('H5环境模拟SQLite:', sql, params)
              resolve({ rows: [] })
            })
          }
        }
        console.log('H5环境SQLite模拟器已初始化')
        // #endif

        // 创建表
        this.createTable().then(() => {
          console.log('数据库表创建成功')
          resolve()
        }).catch((error) => {
          console.error('创建表失败:', error)
          reject(error)
        })
        
      } catch (error) {
        console.error('数据库初始化失败:', error)
        reject(error)
      }
    })
  }

  /**
   * 创建资产数据表
   */
  private async createTable(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS assets (
        id INTEGER PRIMARY KEY,
        number TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        model TEXT,
        categoryCode TEXT,
        imageUrl TEXT,
        status TEXT,
        storageLocation TEXT,
        entryDate TEXT,
        additionMethod TEXT,
        originalValue REAL,
        serviceLife INTEGER,
        expiryYear TEXT,
        department TEXT,
        factoryArea TEXT,
        userId TEXT,
        createTime TEXT,
        version TEXT
      )
    `

    await this.executeSQL(createTableSQL)
    
    // 创建索引以提高查询性能
    const createIndexSQL = `
      CREATE INDEX IF NOT EXISTS idx_assets_number ON assets(number)
    `
    await this.executeSQL(createIndexSQL)
  }

  /**
   * 执行SQL语句
   */
  private async executeSQL(sql: string, params: any[] = []): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('数据库未初始化，无法执行SQL:', sql)
        reject(new Error('数据库未初始化'))
        return
      }

      console.log('执行SQL:', sql, '参数:', params)

      // #ifdef APP-PLUS
      try {
        this.db.transaction((tx: any) => {
          tx.executeSql(sql, params, (tx: any, result: any) => {
            console.log('SQL执行成功:', result)
            resolve(result)
          }, (tx: any, error: any) => {
            console.error('SQL执行失败:', error)
            reject(error)
          })
        }, (error: any) => {
          console.error('事务执行失败:', error)
          reject(error)
        })
      } catch (error) {
        console.error('执行SQL时发生异常:', error)
        reject(error)
      }
      // #endif

      // #ifdef H5
      this.db.executeSql(sql, params).then(resolve).catch(reject)
      // #endif
    })
  }

  /**
   * 插入单条资产数据
   */
  async insertAsset(asset: AssetData): Promise<void> {
    const insertSQL = `
      INSERT OR REPLACE INTO assets (
        id, number, name, model, categoryCode, imageUrl, status,
        storageLocation, entryDate, additionMethod, originalValue,
        serviceLife, expiryYear, department, factoryArea, userId,
        createTime, version
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `

    const params = [
      asset.id,
      asset.number,
      asset.name,
      asset.model,
      asset.categoryCode,
      asset.imageUrl,
      asset.status,
      asset.storageLocation,
      asset.entryDate,
      asset.additionMethod,
      asset.originalValue,
      asset.serviceLife,
      asset.expiryYear,
      asset.department,
      asset.factoryArea,
      asset.userId,
      asset.createTime,
      asset.version
    ]

    await this.executeSQL(insertSQL, params)
  }

  /**
   * 批量插入资产数据
   */
  async insertAssets(assets: AssetData[]): Promise<void> {
    for (const asset of assets) {
      await this.insertAsset(asset)
    }
  }

  /**
   * 根据number范围查询资产数据
   */
  async queryAssetsByNumberRange(queryRange: QueryRange): Promise<AssetData[]> {
    let sql = 'SELECT * FROM assets WHERE 1=1'
    const params: any[] = []

    if (queryRange.startNumber) {
      sql += ' AND number >= ?'
      params.push(queryRange.startNumber)
    }

    if (queryRange.endNumber) {
      sql += ' AND number <= ?'
      params.push(queryRange.endNumber)
    }

    sql += ' ORDER BY number'

    if (queryRange.limit) {
      sql += ' LIMIT ?'
      params.push(queryRange.limit)
    }

    if (queryRange.offset) {
      sql += ' OFFSET ?'
      params.push(queryRange.offset)
    }

    const result = await this.executeSQL(sql, params)
    
    // #ifdef APP-PLUS
    const assets: AssetData[] = []
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i)
      assets.push({
        id: row.id,
        number: row.number,
        name: row.name,
        model: row.model,
        categoryCode: row.categoryCode,
        imageUrl: row.imageUrl,
        status: row.status,
        storageLocation: row.storageLocation,
        entryDate: row.entryDate,
        additionMethod: row.additionMethod,
        originalValue: row.originalValue,
        serviceLife: row.serviceLife,
        expiryYear: row.expiryYear,
        department: row.department,
        factoryArea: row.factoryArea,
        userId: row.userId,
        createTime: row.createTime,
        version: row.version
      })
    }
    return assets
    // #endif

    // #ifdef H5
    return result.rows || []
    // #endif
  }

  /**
   * 根据number精确查询
   */
  async queryAssetByNumber(number: string): Promise<AssetData | null> {
    const sql = 'SELECT * FROM assets WHERE number = ?'
    const result = await this.executeSQL(sql, [number])
    
    // #ifdef APP-PLUS
    if (result.rows.length > 0) {
      const row = result.rows.item(0)
      return {
        id: row.id,
        number: row.number,
        name: row.name,
        model: row.model,
        categoryCode: row.categoryCode,
        imageUrl: row.imageUrl,
        status: row.status,
        storageLocation: row.storageLocation,
        entryDate: row.entryDate,
        additionMethod: row.additionMethod,
        originalValue: row.originalValue,
        serviceLife: row.serviceLife,
        expiryYear: row.expiryYear,
        department: row.department,
        factoryArea: row.factoryArea,
        userId: row.userId,
        createTime: row.createTime,
        version: row.version
      }
    }
    return null
    // #endif

    // #ifdef H5
    return result.rows && result.rows.length > 0 ? result.rows[0] : null
    // #endif
  }

  /**
   * 获取所有资产数据
   */
  async getAllAssets(): Promise<AssetData[]> {
    return this.queryAssetsByNumberRange({})
  }

  /**
   * 获取资产总数
   */
  async getAssetCount(): Promise<number> {
    const result = await this.executeSQL('SELECT COUNT(*) as count FROM assets')
    
    // #ifdef APP-PLUS
    return result.rows.item(0).count
    // #endif

    // #ifdef H5
    return result.rows && result.rows.length > 0 ? result.rows[0].count : 0
    // #endif
  }

  /**
   * 删除所有资产数据
   */
  async clearAllAssets(): Promise<void> {
    await this.executeSQL('DELETE FROM assets')
  }

  /**
   * 关闭数据库连接
   */
  async closeDatabase(): Promise<void> {
    // #ifdef APP-PLUS
    if (this.db) {
      plus.sqlite.closeDatabase(this.db)
      this.db = null
    }
    // #endif
  }
}

// 创建单例实例
export const sqliteManager = new SQLiteManager()

// 导出类型和实例
export { SQLiteManager }
