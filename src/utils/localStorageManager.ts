/**
 * LocalStorage 资产数据管理器
 * 在真机上提供可靠的数据持久化
 */

import { AssetData, QueryRange } from '../types/asset'

// 存储键名常量
const STORAGE_KEYS = {
  ASSETS: 'assets_data',
  METADATA: 'assets_metadata',
  VERSION: 'assets_version'
} as const

// 元数据接口
interface StorageMetadata {
  totalCount: number
  lastUpdate: string
  version: string
}

class LocalStorageManager {
  private storagePrefix = 'asset_app_'

  /**
   * 获取完整的存储键名
   */
  private getStorageKey(key: string): string {
    return `${this.storagePrefix}${key}`
  }

  /**
   * 初始化存储
   */
  async initStorage(): Promise<void> {
    try {
      console.log('=== LocalStorage 初始化 ===')

      // 检查localStorage是否可用
      if (typeof uni.getStorageSync !== 'function') {
        throw new Error('uni.getStorageSync 不可用')
      }

      // 测试读写功能
      const testKey = this.getStorageKey('test')
      uni.setStorageSync(testKey, 'test_value')
      const testValue = uni.getStorageSync(testKey)

      if (testValue !== 'test_value') {
        throw new Error('localStorage 读写测试失败')
      }

      // 清理测试数据
      uni.removeStorageSync(testKey)

      // 初始化元数据（如果不存在）
      const metadata = this.getMetadata()
      if (!metadata) {
        this.setMetadata({
          totalCount: 0,
          lastUpdate: new Date().toISOString(),
          version: '1.0.0'
        })
      }

      console.log('LocalStorage 初始化成功')
      console.log('当前存储状态:', this.getStorageInfo())

    } catch (error) {
      console.error('LocalStorage 初始化失败:', error)
      throw error
    }
  }

  /**
   * 插入单条资产数据
   */
  async insertAsset(asset: AssetData): Promise<void> {
    try {
      const assets = this.getAllAssetsSync()

      // 检查是否已存在（根据number去重）
      const existingIndex = assets.findIndex(item => item.number === asset.number)

      if (existingIndex >= 0) {
        // 更新现有数据
        assets[existingIndex] = asset
        console.log(`更新资产数据: ${asset.number}`)
      } else {
        // 添加新数据
        assets.push(asset)
        console.log(`插入新资产数据: ${asset.number}`)
      }

      // 保存数据
      this.saveAllAssets(assets)

    } catch (error) {
      console.error('插入资产数据失败:', error)
      throw error
    }
  }

  /**
   * 批量插入资产数据
   */
  async insertAssets(newAssets: AssetData[]): Promise<void> {
    try {
      console.log(`开始批量插入 ${newAssets.length} 条资产数据`)

      const existingAssets = this.getAllAssetsSync()
      const assetMap = new Map<string, AssetData>()

      // 将现有数据放入Map（以number为键）
      existingAssets.forEach(asset => {
        assetMap.set(asset.number, asset)
      })

      // 添加或更新新数据
      let insertCount = 0
      let updateCount = 0

      newAssets.forEach(asset => {
        if (assetMap.has(asset.number)) {
          updateCount++
        } else {
          insertCount++
        }
        assetMap.set(asset.number, asset)
      })

      // 转换回数组并保存
      const allAssets = Array.from(assetMap.values())
      this.saveAllAssets(allAssets)

      console.log(`批量插入完成: 新增 ${insertCount} 条，更新 ${updateCount} 条`)

    } catch (error) {
      console.error('批量插入资产数据失败:', error)
      throw error
    }
  }

  /**
   * 根据number范围查询资产数据
   */
  async queryAssetsByNumberRange(queryRange: QueryRange): Promise<AssetData[]> {
    try {
      let assets = this.getAllAssetsSync()

      // 按number排序
      assets.sort((a, b) => a.number.localeCompare(b.number))

      // 应用范围过滤
      if (queryRange.startNumber) {
        assets = assets.filter(asset => asset.number >= queryRange.startNumber!)
      }

      if (queryRange.endNumber) {
        assets = assets.filter(asset => asset.number <= queryRange.endNumber!)
      }

      // 应用分页
      if (queryRange.offset) {
        assets = assets.slice(queryRange.offset)
      }

      if (queryRange.limit) {
        assets = assets.slice(0, queryRange.limit)
      }

      console.log(`范围查询完成，返回 ${assets.length} 条数据`)
      return assets

    } catch (error) {
      console.error('范围查询失败:', error)
      throw error
    }
  }

  /**
   * 根据number精确查询
   */
  async queryAssetByNumber(number: string): Promise<AssetData | null> {
    try {
      const assets = this.getAllAssetsSync()
      const asset = assets.find(item => item.number === number)

      console.log(`精确查询 ${number}:`, asset ? '找到' : '未找到')
      return asset || null

    } catch (error) {
      console.error('精确查询失败:', error)
      throw error
    }
  }

  /**
   * 获取所有资产数据
   */
  async getAllAssets(): Promise<AssetData[]> {
    return this.getAllAssetsSync()
  }

  /**
   * 获取资产总数
   */
  async getAssetCount(): Promise<number> {
    try {
      const assets = this.getAllAssetsSync()
      return assets.length
    } catch (error) {
      console.error('获取资产总数失败:', error)
      return 0
    }
  }

  /**
   * 删除所有资产数据
   */
  async clearAllAssets(): Promise<void> {
    try {
      uni.removeStorageSync(this.getStorageKey(STORAGE_KEYS.ASSETS))
      this.setMetadata({
        totalCount: 0,
        lastUpdate: new Date().toISOString(),
        version: '1.0.0'
      })
      console.log('已清空所有资产数据')
    } catch (error) {
      console.error('清空资产数据失败:', error)
      throw error
    }
  }

  /**
   * 同步获取所有资产数据
   */
  private getAllAssetsSync(): AssetData[] {
    try {
      const data = uni.getStorageSync(this.getStorageKey(STORAGE_KEYS.ASSETS))
      return Array.isArray(data) ? data : []
    } catch (error) {
      console.error('获取资产数据失败:', error)
      return []
    }
  }

  /**
   * 保存所有资产数据
   */
  private saveAllAssets(assets: AssetData[]): void {
    try {
      uni.setStorageSync(this.getStorageKey(STORAGE_KEYS.ASSETS), assets)

      // 更新元数据
      this.setMetadata({
        totalCount: assets.length,
        lastUpdate: new Date().toISOString(),
        version: '1.0.0'
      })

    } catch (error) {
      console.error('保存资产数据失败:', error)
      throw error
    }
  }

  /**
   * 获取元数据
   */
  private getMetadata(): StorageMetadata | null {
    try {
      const data = uni.getStorageSync(this.getStorageKey(STORAGE_KEYS.METADATA))
      return data || null
    } catch (error) {
      console.error('获取元数据失败:', error)
      return null
    }
  }

  /**
   * 设置元数据
   */
  private setMetadata(metadata: StorageMetadata): void {
    try {
      uni.setStorageSync(this.getStorageKey(STORAGE_KEYS.METADATA), metadata)
    } catch (error) {
      console.error('设置元数据失败:', error)
      throw error
    }
  }

  /**
   * 获取存储信息
   */
  getStorageInfo(): any {
    try {
      const metadata = this.getMetadata()
      const assets = this.getAllAssetsSync()

      // 计算存储大小（估算）
      const dataSize = JSON.stringify(assets).length
      const sizeInKB = Math.round(dataSize / 1024 * 100) / 100

      return {
        totalCount: assets.length,
        metadata,
        estimatedSize: `${sizeInKB} KB`,
        lastUpdate: metadata?.lastUpdate || '未知'
      }
    } catch (error) {
      console.error('获取存储信息失败:', error)
      return {
        totalCount: 0,
        metadata: null,
        estimatedSize: '0 KB',
        lastUpdate: '未知'
      }
    }
  }

  /**
   * 导出数据（用于备份）
   */
  exportData(): string {
    try {
      const assets = this.getAllAssetsSync()
      const metadata = this.getMetadata()

      return JSON.stringify({
        assets,
        metadata,
        exportTime: new Date().toISOString()
      }, null, 2)
    } catch (error) {
      console.error('导出数据失败:', error)
      throw error
    }
  }

  /**
   * 导入数据（用于恢复）
   */
  async importData(jsonData: string): Promise<void> {
    try {
      const data = JSON.parse(jsonData)

      if (!Array.isArray(data.assets)) {
        throw new Error('无效的数据格式')
      }

      this.saveAllAssets(data.assets)
      console.log(`导入数据成功，共 ${data.assets.length} 条记录`)

    } catch (error) {
      console.error('导入数据失败:', error)
      throw error
    }
  }
}

// 创建单例实例
export const localStorageManager = new LocalStorageManager()

// 导出类型和实例
export { LocalStorageManager, AssetData, QueryRange }
