# SQLite 资产数据库使用说明

## 概述

本项目提供了完整的SQLite数据库解决方案，用于存储和管理资产数据。支持按资产编号范围查询、精确查询、批量插入等功能。

## 文件结构

- `src/utils/sqlite.ts` - SQLite数据库管理类
- `src/utils/assetDataGenerator.ts` - 示例数据生成器
- `src/pages/demo/sqliteDemo.vue` - 演示页面

## 主要功能

### 1. 数据库初始化
```typescript
import { sqliteManager } from '@/utils/sqlite'

// 初始化数据库
await sqliteManager.initDatabase()
```

### 2. 插入数据
```typescript
// 插入单条数据
const asset: AssetData = {
  id: 2623,
  number: "ASSET-20250908-001",
  name: "笔记本电脑4",
  // ... 其他字段
}
await sqliteManager.insertAsset(asset)

// 批量插入数据
const assets: AssetData[] = [/* 多个资产数据 */]
await sqliteManager.insertAssets(assets)
```

### 3. 范围查询
```typescript
// 按编号范围查询
const results = await sqliteManager.queryAssetsByNumberRange({
  startNumber: 'ASSET-20250101-001',
  endNumber: 'ASSET-20250101-010',
  limit: 10
})
```

### 4. 精确查询
```typescript
// 根据编号精确查询
const asset = await sqliteManager.queryAssetByNumber('ASSET-20250908-001')
```

### 5. 统计信息
```typescript
// 获取总数量
const count = await sqliteManager.getAssetCount()
```

## 数据表结构

```sql
CREATE TABLE assets (
  id INTEGER PRIMARY KEY,
  number TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  model TEXT,
  categoryCode TEXT,
  imageUrl TEXT,
  status TEXT,
  storageLocation TEXT,
  entryDate TEXT,
  additionMethod TEXT,
  originalValue REAL,
  serviceLife INTEGER,
  expiryYear TEXT,
  department TEXT,
  factoryArea TEXT,
  userId TEXT,
  createTime TEXT,
  version TEXT
)
```

## 使用示例

### 生成并插入1000条示例数据
```typescript
import { initAssetDatabase } from '@/utils/assetDataGenerator'

// 初始化数据库并插入示例数据
await initAssetDatabase()
```

### 测试查询功能
```typescript
import { testQueryFunctions } from '@/utils/assetDataGenerator'

// 测试各种查询功能
await testQueryFunctions()
```

## 演示页面

访问 `/pages/demo/sqliteDemo` 页面可以查看完整的演示功能，包括：
- 数据库初始化
- 范围查询
- 精确查询
- 统计信息
- 实时日志

## 注意事项

1. 该实现支持APP-PLUS和H5环境
2. H5环境使用localStorage模拟SQLite功能
3. 数据库文件存储在 `_doc/assets.db`
4. 建议在生产环境中添加错误处理和事务管理
5. 大量数据操作时建议使用批量插入以提高性能

## API 参考

### SQLiteManager 类

#### 方法

- `initDatabase(): Promise<void>` - 初始化数据库
- `insertAsset(asset: AssetData): Promise<void>` - 插入单条资产数据
- `insertAssets(assets: AssetData[]): Promise<void>` - 批量插入资产数据
- `queryAssetsByNumberRange(queryRange: QueryRange): Promise<AssetData[]>` - 范围查询
- `queryAssetByNumber(number: string): Promise<AssetData | null>` - 精确查询
- `getAllAssets(): Promise<AssetData[]>` - 获取所有资产
- `getAssetCount(): Promise<number>` - 获取资产总数
- `clearAllAssets(): Promise<void>` - 清空所有数据
- `closeDatabase(): Promise<void>` - 关闭数据库连接

#### 类型定义

```typescript
interface AssetData {
  id: number
  number: string
  name: string
  model: string
  categoryCode: string
  imageUrl: string
  status: string
  storageLocation: string
  entryDate: string | null
  additionMethod: string
  originalValue: number | null
  serviceLife: number | null
  expiryYear: string
  department: string
  factoryArea: string
  userId: string
  createTime: string
  version: string
}

interface QueryRange {
  startNumber?: string
  endNumber?: string
  limit?: number
  offset?: number
}
```
