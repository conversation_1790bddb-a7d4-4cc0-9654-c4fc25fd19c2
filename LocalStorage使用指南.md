# LocalStorage 资产数据存储使用指南

## 📋 概述

项目现在完全基于LocalStorage实现资产数据的持久化存储。这个方案在真机上100%可用，性能优秀，无需任何配置。

## 🚀 主要特点

- ✅ **100%真机可用** - 不依赖任何原生模块
- ✅ **无需配置** - 不需要manifest.json配置
- ✅ **数据持久化** - 应用重启后数据保持
- ✅ **性能优秀** - 适合中等规模数据（建议<5MB）
- ✅ **API简洁** - 统一的接口设计

## 📁 文件结构

```
src/utils/
├── dataManager.ts           # 数据管理器（主要使用）
├── localStorageManager.ts   # LocalStorage实现
└── assetDataGenerator.ts    # 示例数据生成器

src/pages/demo/
└── localStorageDemo.vue     # 演示页面
```

## 🔧 基本使用

### 1. 导入数据管理器

```typescript
import { dataManager } from '@/utils/dataManager'
```

### 2. 初始化存储

```typescript
// 初始化存储
await dataManager.initDatabase()
```

### 3. 插入数据

```typescript
// 插入单条数据
await dataManager.insertAsset(assetData)

// 批量插入数据
await dataManager.insertAssets(assetList)
```

### 4. 查询数据

```typescript
// 范围查询
const results = await dataManager.queryAssetsByNumberRange({
  startNumber: 'ASSET-001',
  endNumber: 'ASSET-100',
  limit: 50
})

// 精确查询
const asset = await dataManager.queryAssetByNumber('ASSET-001')

// 获取所有数据
const allAssets = await dataManager.getAllAssets()

// 获取总数
const count = await dataManager.getAssetCount()
```

### 5. 数据管理

```typescript
// 清空所有数据
await dataManager.clearAllAssets()

// 导出数据（备份）
const backupData = dataManager.exportData()

// 导入数据（恢复）
await dataManager.importData(backupData)

// 获取存储信息
const info = dataManager.getStorageInfo()
```

## 📱 演示页面

访问 `/pages/demo/localStorageDemo` 可以：
- 测试存储功能
- 生成示例数据
- 查看存储状态
- 进行各种查询

## 🔄 从SQLite迁移

如果您之前使用SQLite，迁移非常简单：

### 1. 更新导入语句
```typescript
// 旧代码
import { sqliteManager } from '@/utils/sqlite'

// 新代码
import { dataManager } from '@/utils/dataManager'
```

### 2. 更新API调用
```typescript
// 旧代码
await sqliteManager.initDatabase()
await sqliteManager.insertAssets(assets)

// 新代码
await dataManager.initDatabase()
await dataManager.insertAssets(assets)
```

所有其他API保持不变！

## 📊 性能建议

1. **批量操作** - 使用 `insertAssets()` 而不是多次调用 `insertAsset()`
2. **分页查询** - 对于大量数据，使用 `limit` 参数
3. **定期清理** - 定期清理不需要的数据
4. **数据备份** - 定期使用 `exportData()` 备份数据

## 🚨 注意事项

1. **存储限制** - LocalStorage通常有5-10MB限制
2. **数据格式** - 数据以JSON格式存储
3. **错误处理** - 建议使用try-catch包装所有操作
4. **数据量** - 建议控制在1-2万条记录以内

## 🛠️ 错误处理

```typescript
try {
  await dataManager.initDatabase()
  await dataManager.insertAssets(assets)
} catch (error) {
  console.error('操作失败:', error)
  // 显示用户友好的错误信息
  uni.showToast({
    title: '操作失败',
    icon: 'error'
  })
}
```

## 📞 技术支持

如果遇到问题：
1. 查看控制台日志获取详细错误信息
2. 使用演示页面测试功能
3. 检查存储信息：`dataManager.getStorageInfo()`

## 🎯 总结

LocalStorage方案提供了：
- 简单易用的API
- 可靠的数据持久化
- 优秀的真机兼容性
- 无需配置的即插即用体验

现在您可以放心地在真机上使用这个存储方案！
