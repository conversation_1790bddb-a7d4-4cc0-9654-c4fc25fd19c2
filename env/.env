VITE_APP_TITLE = 'JeecgBoot-uniapp'
VITE_APP_PORT = 9000

VITE_UNI_APPID = '__UNI__643A408'
VITE_WX_APPID = 'wx8e287639924edb51'

# h5部署网站的base，配置到 manifest.config.ts 里的 h5.router.base
VITE_APP_PUBLIC_BASE=/

VITE_SERVER_BASEURL = 'http://192.168.1.31:8080/jeecg-boot'；
# 上传接口
VITE_UPLOAD_BASEURL = ''

# 有些同学可能需要在微信小程序里面根据 develop、trial、release 分别设置上传地址，参考代码如下。
# 下面的变量如果没有设置，会默认使用 VITE_SERVER_BASEURL or VITE_UPLOAD_BASEURL
VITE_SERVER_BASEURL__WEIXIN_DEVELOP = ''
VITE_SERVER_BASEURL__WEIXIN_TRIAL = ''
VITE_SERVER_BASEURL__WEIXIN_RELEASE = ''

VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP = ''
VITE_UPLOAD_BASEURL__WEIXIN_TRIAL = ''
VITE_UPLOAD_BASEURL__WEIXIN_RELEASE = ''

# h5是否需要配置代理
VITE_APP_PROXY= true
VITE_APP_PROXY_PREFIX = '/api'

# 是否启用mock (1.仅支持h5 2.启用必须要开启代理，否则生效。)
VITE_USE_MOCK = true
